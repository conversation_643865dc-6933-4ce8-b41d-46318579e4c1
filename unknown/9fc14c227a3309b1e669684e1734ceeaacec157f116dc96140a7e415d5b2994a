SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER PROCEDURE [MTX].[GetPredictiveLeadDetails] --61001594
(
    @LeadId BIGINT,
    @UserId BIGINT = 0,
    @ReasonID INT = 0
)
AS
BEGIN
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    SET NOCOUNT ON;
    SET DEADLOCK_PRIORITY LOW;

    -- Declare variables
    DECLARE @AssigntoUserID BIGINT = 0;

    -- Create a temporary table to store lead details
    CREATE TABLE #TempLeadDetails
    (
        LeadID BIGINT,
        ParentID BIGINT,
        MobileNo BIGINT,
        ProductID INT,
        AssignedToUserID BIGINT,
        EmployeeId VARCHAR(30),
        LeadSource VARCHAR(100),
        CustomerID BIGINT,
        CustName VARCHAR(200),
        CountryCode INT,
        Asterisk_IP VARCHAR(100),
        CalledEmp VARCHAR(30),
        CountryId INT,
        IsCalledAgent BIGINT,
		SubProductId TINYINT,
        NriCity VARCHAR(250),
        CurrentAs<PERSON>UserID BIGINT,
        AssignedToGroupId SMALLINT,
        StateID SMALLINT
    );
	
	DECLARE @BookedLeadID BIGINT=0
	DECLARE @SalesAgent BIGINT = 0;
	--Check if Leadid is not present in [Matrix].[CRM].[Leaddetails]
	IF @ReasonID = 30 
		BEGIN
			SELECT TOP 1 @SalesAgent= SALESAGENT,@BookedLeadID= LD.LEADID 
			FROM		MATRIX.CRM.Leaddetails (NOLOCK) LD
			INNER JOIN	MATRIX.MTX.AdditionalBookingDetails (NOLOCK) ABD ON ABD.LEADID = LD.LEADID 
			WHERE		(LD.ParentID = @LeadId OR LD.LeadID = @LeadId) 
			AND			LD.ProductID IN(7,115) 
		END

    -- Insert lead details into the temporary table
    INSERT INTO #TempLeadDetails(LeadID, ParentID, MobileNo, ProductID, LeadSource, CustomerID, CustName, CountryCode, CalledEmp, CountryId, IsCalledAgent, SubProductId, StateID)
    SELECT TOP 1 @LeadId,
                LD.ParentID,
                LD.MobileNo,
                LD.ProductID,
                LD.LeadSource,
                LD.CustomerID,
                LD.Name AS CustName,
                CASE WHEN country.CountryCode=0 THEN 91 ELSE country.CountryCode END  AS CountryCode,
                LUD.EmployeeId AS CalledEmp,
                CASE WHEN ISNULL(country.CountryCode,0)=0 THEN 392 ELSE country.CountryId END  AS CountryId,
                ISNULL(CUD.UserID, 0) AS IsCalledAgent,
				PD.InvestmentTypeID,
                LD.StateID
    FROM [Matrix].[CRM].[Leaddetails] AS LD WITH (NOLOCK) 
	INNER JOIN CRM.ProductDetails (NOLOCK) PD ON PD.LeadID=LD.LeadID
    LEFT JOIN	Master.Country country (NOLOCK) ON country.CountryId=CASE WHEN LD.Country in ('','0','91','+91','INDIA','392') THEN 392
	WHEN ISNUMERIC(LD.Country) = 1 THEN LD.COUNTRY ELSE NULL END
    LEFT JOIN CRM.UserDetails LUD WITH (NOLOCK) ON LUD.UserID = @UserId
    LEFT JOIN CRM.UserDetails CUD WITH (NOLOCK) ON LD.MobileNo = CUD.DIDNo
    WHERE LD.LeadID = CASE WHEN  @BookedLeadID > 0 THEN @BookedLeadID ELSE @LeadId END;

    -- Update AssignedToUserID based on last assigned details
    UPDATE		TLD
    SET			TLD.AssignedToUserID = LAD.AssignedToUserID,@AssigntoUserID = LAD.AssignedToUserID, TLD.CurrentAssignedUserID = LAD.AssignedToUserID, TLD.AssignedToGroupId = LAD.AssignToGroupId
    FROM		#TempLeadDetails TLD 
    INNER JOIN	[Matrix].[CRM].[LeadAssignDetails] AS LAD ON CASE WHEN TLD.ParentID IS NULL THEN TLD.LeadID ELSE TLD.ParentID END = LAD.LeadID AND LAD.IsLastAssigned=1;

    -- if TL is calling he can call only in his span
    IF EXISTS(SELECT TOP 1 * FROM CRM.UserGroupRoleMapNew ugrn (NOLOCK)
                INNER JOIN CRM.RoleSuperMaster RSM ON ugrn.RoleSuperId=RSM.RoleSuperId WHERE ugrn.UserId=@UserId AND RSM.RoleId=12)
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM CRM.UserDetails WHERE UserID IN (SELECT AssignedToUserID FROM #TempLeadDetails) AND ManagerId=@UserId)
                BEGIN
                    DELETE FROM #TempLeadDetails;
                END
        END                


    -- if salesagent not found in above query , then for BHR update salesagent from ABD
    IF EXISTS (SELECT 1 FROM #TempLeadDetails WHERE ProductID IN (115,7)) AND @ReasonID = 30
		BEGIN						
			UPDATE		TLD
			SET			TLD.AssignedToUserID = CASE WHEN @SalesAgent > 0 THEN @SalesAgent ELSE TLD.AssignedToUserID END,
						@AssigntoUserID =	   CASE WHEN @SalesAgent > 0 THEN @SalesAgent ELSE TLD.AssignedToUserID END
			FROM		#TempLeadDetails TLD                        
		END

    -- Update EmployeeId and Asterisk_IP
    UPDATE		TLD
    SET			TLD.EmployeeId = UD.EmployeeId,
				TLD.Asterisk_IP = UD.Asterisk_IP
    FROM		#TempLeadDetails TLD
    INNER JOIN	CRM.UserDetails AS UD ON TLD.AssignedToUserID = UD.UserID

    
    IF EXISTS (SELECT 1 FROM #TempLeadDetails WHERE ProductID IN (2) AND CountryCode in (1))
        BEGIN						
			UPDATE		TLD
			SET			TLD.NriCity = HD.NriCity
			FROM		#TempLeadDetails TLD    
            INNER JOIN	MTX.HealthDetails AS HD ON TLD.LeadID = HD.LeadID           
		END
    -- Return lead details
    IF ISNULL(@AssigntoUserID,0) = 0
		BEGIN
			DELETE FROM #TempLeadDetails;
		END;
	--SELECT ALL LeadDetails
	SELECT * FROM #TempLeadDetails
	
    -- Drop temporary table
    DROP TABLE IF EXISTS #TempLeadDetails
END
GO
