ALTER PROCEDURE [CARE].[GetSalesProcessMaster] (
    @UserId BIGINT=0
)
AS
BEGIN

    SET NOCOUNT ON
    SET DEADLOCK_PRIORITY LOW  
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    DECLARE @UserProductTbl TABLE( ProductId smallint )

	INSERT INTO @UserProductTbl(ProductId)
	SELECT DISTINCT CASE
        WHEN PGM.ProductId = 1000 THEN 7
        WHEN PGM.ProductId = 1001 THEN 115
        ELSE PGM.ProductId
        END AS ProductId 
    FROM  CRM.UserGroupRoleMapNew UGRM  WITH(NOLOCK)
	INNER JOIN CRM.ProductGroupMapping PGM WITH(NOLOCK) ON UGRM.GroupId = PGM.GroupId 		
	WHERE UGRM.UserId = @UserId;

    select [Key] as ProcessID,Name as ProcessName from [Matrix].[CARE].[SalesTicketMaster] with(nolock)
    where MasterType='process' 
        and [Key] not in(4,5,11) 
        and ([key] != 9 OR EXISTS (SELECT 1 FROM @UserProductTbl WHERE ProductId = 115)) /*show verification process only for saving users*/
		and ([key] not in(12,13) OR EXISTS (SELECT 1 FROM @UserProductTbl WHERE ProductId = 2)); /*show mis-sell and claims process only for health users*/
        and ([key] != 14 OR EXISTS (SELECT 1 FROM @UserProductTbl WHERE ProductId = 117)); /*show pricing process only for motor users*/

END
