SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- EXEC [CARE].[GetSalesTicketUserDetails] @userId=90022
ALTER PROCEDURE [CARE].[GetSalesTicketUserDetails]
   (
   @userId int
	)
AS
BEGIN
	
	SET NOCOUNT ON;
	SET DEADLOCK_PRIORITY LOW;	                    
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
  
	declare @RoleId smallint=0;
	--declare @UserType TINYINT=0;
	declare @BU smallint=0;
	declare  @prdtbl table(productId  smallint);


	SELECT top(1) @RoleId=RSM.RoleId
		from pbcroma.CRM.[UserGroupRoleMapNew] UGRMN 
		INNER JOIN pbcroma.CRM.[UserDetails] UD ON UD.UserID=UGRMN.UserID AND UD.IsActive=1 
		INNER JOIN  pbcroma.CRM.[RoleSuperMaster] RSM ON UGRMN.RoleSuperId=RSM.RoleSuperId    
		INNER JOIN CRM.PermissionDetails PD ON UGRMN.UserId=PD.UserId  
		WHERE  ud.UserID=@userId;
		
		IF(@RoleId=13)
		BEGIN
			
			select @BU=[CARE].[GetFeedbackUserMainPrd](@userId)	
			
			-- If agent product is 2 and is renewal agent then update the productId as 147 (Health Renewal) 
			IF @BU = 2
				BEGIN
					DECLARE @IsRenewal BIT=0;
					DECLARE @RenewalResult TABLE (IsRenewal BIT);
					
					INSERT INTO @RenewalResult
					EXEC MTX.CheckRenwalAgent @UserID = @userId;
					SELECT @IsRenewal = IsRenewal FROM @RenewalResult;

					IF @IsRenewal = 1
						SET @BU = 147
				END		
		END


 IF NOT Exists(SELECT TOP(1) 1 from  [Matrix].[CARE].[SalesUserDetails] with(nolock) where MatrixUserID=@userId)
 begin 

      DECLARE @ticketUserId int=0
	 Insert into [Matrix].[CARE].[SalesUserDetails](MatrixUserID,UserLevel,TicketRoleID,CreatedOn,BU)
	 VALUES(@userId,0,13,GETDATE(),@BU)

	 SET @ticketUserId=SCOPE_IDENTITY();		
 end
	 IF(@RoleId=13)
	 begin
	   update [Matrix].[CARE].[SalesUserDetails] set BU=@BU where  MatrixUserID=@userId;
	 end

	  declare @usertable Table(UserId bigint,EmployeeId varchar(100),UserName  varchar(100),
                           MatrixUserID int,ProcessID int,RoleName varchar(100),RoleId int,
						   BU	int ,UserLevel int, MatrixRoleId int)

	INSERT @usertable( UserID,EmployeeId,UserName,MatrixUserID,ProcessID,RoleName,RoleId,BU,UserLevel)
	select ud.UserID,ud.EmployeeId,ud.UserName,salesusertbl.MatrixUserID,isnull(processmapping.ProcessID,0) as ProcessID
	,'Supervisior' as RoleName,salesusertbl.TicketRoleID as RoleId,
	ISNULL(BU,0) as BU,salesusertbl.UserLevel
	from [Matrix].[CARE].[SalesUserDetails] salesusertbl with(nolock)
	inner join pbcroma.CRM.[UserDetails] ud with(nolock) on salesusertbl.MatrixUserID=ud.UserID
	left join [Matrix].[CARE].[SalesUserProcessMapping] processmapping with(nolock) on salesusertbl.TicketUserID=processmapping.UserID
	where ud.UserID=@userId;

    UPDATE @usertable SET MatrixRoleId = @RoleId WHERE UserID = @userId;

	IF [CRM].[GetUserRoleId](@userId)!=13
	BEGIN
	   UPDATE @usertable  set UserLevel=4 where  UserLevel=0
	END
	select UserID,EmployeeId,UserName,MatrixUserID,ProcessID,RoleName,RoleId,BU,UserLevel,MatrixRoleId from @usertable;
 
 
  
END


GO
