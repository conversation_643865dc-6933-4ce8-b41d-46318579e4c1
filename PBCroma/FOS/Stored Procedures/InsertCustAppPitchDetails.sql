CREATE PROCEDURE FOS.InsertCustAppPitchDetails      
    @AppointmentId INT,      
    @LeadId INT,      
    @CallDataId BIGINT,      
    @home_visit_pitch BIT,      
    @customer_agree BIT,      
    @customer_refusal BIT,      
    @meeting_time VARCHAR(50)      
AS      
BEGIN      
    
    Declare @isApp bit=0;    
 Select top 1 @isApp= AppointmentId from FOS.CustAppPitchDetails WHERE AppointmentId=@AppointmentId    
    
 if @isApp>0    
      BEGIN    
      UPDATE CAP    
   set updatedOn=GETDATE(),    
       CallDataId=@CallDataId,    
    home_visit_pitch=@home_visit_pitch,    
    customer_agree=@customer_agree,    
    meeting_time=@meeting_time    
         FROM  FOS.CustAppPitchDetails CAP WITH (NOLOCK)    
   WHERE AppointmentId=@AppointmentId    
         
   END    
 ELSE     
 BEGIN    
    INSERT INTO FOS.CustAppPitchDetails (      
        AppointmentId, LeadId, CallDataId,      
        home_visit_pitch, customer_agree, customer_refusal, meeting_time, CreatedOn,UpdatedOn    
    )      
    VALUES (      
        @AppointmentId, @LeadId, @CallDataId,      
        @home_visit_pitch, @customer_agree, @customer_refusal, @meeting_time, GETDATE(),NULL    
    );      
    END    
END