SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [MTX].[StateContextMapping](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[ProductID] [smallint] NOT NULL,
	[LeadSource] [varchar](50) NULL,
	[StateID] [smallint] NOT NULL,
	[Contex] [varchar](50) NOT NULL,
	[IsActive] [bit] NULL
PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
ALTER TABLE [MTX].[StateContextMapping] ADD  DEFAULT ((1)) FOR [IsActive]
GO

