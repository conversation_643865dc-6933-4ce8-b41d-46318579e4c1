CREATE PROCEDURE [MTX].[InsertAICallAnalysisData]
    @LeadID BIGINT,
    @ProductID INT,
    @CallDataID BIGINT,
    @CallDateTime DATETIME,
    @CallbackTime VARCHAR(100),
    @IsDNCMark BIT,
    @DNCMarkReason VARCHAR(300),
    @Actionables VARCHAR(4000),
    @Buckets MTX.AIBucketList READONLY, -- TVP
    @Diseases MTX.AIDiseaseList READONLY -- TVP
AS
BEGIN
	
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
	SET DEADLOCK_PRIORITY LOW

    DECLARE @CustomerID BIGINT;

    SELECT @LeadID = LEADID,@ProductID = PRODUCTID,@CallDateTime = CallDate FROM MTX.CALLDATAHISTORY (NOLOCK) WHERE CALLDATAID = @CallDataID;
    SELECT @CustomerID = CUSTOMERID FROM MATRIX.CRM.LEADDETAILS (NOLOCK) WHERE LEADID = @LeadID;

    INSERT INTO MTX.AICallAnalysisData
    (
       LEADID, CustomerID, ProductID, CallDataID, CallDateTime,
        CallBackTime, IsDNCMark, DNCMarkReason, Actionables, CreatedON
    )
    VALUES
    (
        @LeadID, @CustomerID, @ProductID, @CallDataID, @CallDateTime,
        @CallbackTime, @IsDNCMark, @DNCMarkReason, @Actionables, GETDATE()
    );

    -- Insert buckets
    IF EXISTS (SELECT 1 FROM @Buckets)
    BEGIN
        INSERT INTO MTX.AICallAnalysisBucketingData
        (LEADID, CallDataID, BucketDesc, TriggerPhrase, CreatedON)
        SELECT
            @LeadID,
            @CallDataID,
            BucketName,
            TriggerPhrase,
            GETDATE()
        FROM @Buckets;
    END

    -- Insert medical info
    IF EXISTS (SELECT 1 FROM @Diseases)
    BEGIN
        INSERT INTO MTX.AIAnalysedLeadMedicalInfo
        (LEADID, CUSTOMERID, CallDataID, IsPED, DiseaseName, DiseaseDuration, IsTermPlanAllowed,Relation,Createdon)
        SELECT
            @LeadID,
            @CustomerID,
            @CallDataID,
            1,
            DiseaseName,
            DiseaseDuration,
            IsTermPlanAllowed,
            Relation,
            GETDATE()
        FROM @Diseases;
    END
END
