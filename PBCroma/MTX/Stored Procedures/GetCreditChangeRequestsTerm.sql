﻿-------------------------------------------
--CREATED BY - Bhavesh
--CreatedOn - 00/00/2000
-------------------------------------------
-- exec [MTX].[GetCreditChangeRequestsTerm] 
CREATE PROCEDURE [MTX].[GetCreditChangeRequestsTerm] 
(
    @UserID BIGINT
)
AS 
BEGIN

    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    SET DEADLOCK_PRIORITY LOW;
    SET NOCOUNT ON;

    DECLARE @IsAuthorisedCreator INT = 0;
    DECLARE @IsBulkUploadAuthorized BIT = 0;
    DECLARE @CurrentEmployeeID VARCHAR(20) = '';
    DECLARE @MaxIncentiveMonth DATETIME = '';

	DROP TABLE IF EXISTS #TempCreditChangeApproverMapping;
    DROP TABLE IF EXISTS #TempAgentProcessDetails;
    DROP TABLE IF EXISTS #TempUserIDforRM4;

    -- Check if the user is an authorized creator
	CREATE TABLE #TempCreditChangeApproverMapping(UserID BIGINT,ProductID INT,AgentType INT, ApproverType INT,FloorProcess VARCHAR(255),IsBulkUpload BIT)
    CREATE TABLE #TempUserIDforRM4(UserID BIGINT)
	INSERT INTO #TempCreditChangeApproverMapping(UserID,ProductID,AgentType, ApproverType, FloorProcess, IsBulkUpload)
	SELECT USERID,PRODUCTID,AGENTTYPE,ApproverType,FloorProcess,IsBulkUpload FROM  MTX.CreditChangeApproverMapping WHERE USERID = @UserID AND IsActive = 1 AND ProductID IN (7);

	SELECT 
		@MaxIncentiveMonth = MAX(CASE WHEN productid = 7 AND isactive = 1 THEN IncentiveMonth END)
		FROM mtx.AgentProcessDetails;

	CREATE TABLE #TermUsers(UserID BIGINT,ManagerEmployeeID VARCHAR(20), ProductId INT)
	INSERT INTO #TermUsers(ManagerEmployeeID, ProductId)
	SELECT DISTINCT AMEmployeeId, 7 FROM MTX.AGENTPROCESSDETAILS WHERE PRODUCTID = 7 AND IncentiveMonth >= dateadd(month, -1, @MaxIncentiveMonth);

    INSERT INTO #TermUsers(ManagerEmployeeID, ProductId)
	SELECT DISTINCT ManagerEmployeeId, 7 FROM MTX.AGENTPROCESSDETAILS WHERE PRODUCTID = 7 AND IncentiveMonth >= dateadd(month, -1, @MaxIncentiveMonth);

	UPDATE HRU
	SET UserID = UD.USERID
	FROM #TermUsers HRU
	INNER JOIN CRM.UserDetails UD ON UD.EMPLOYEEID = HRU.ManagerEmployeeID

	CREATE TABLE #TempAgentProcessDetails(AgentID BIGINT, AMEmployeeID VARCHAR(30), TLEmployeeID VARCHAR(30),ManagerEmployeeId VARCHAR(30),ProductID INT,IncentiveMonth Datetime,FloorProcess VARCHAR(200));

    IF EXISTS (SELECT 1 FROM #TempCreditChangeApproverMapping WHERE ApproverType = 1)
    BEGIN
        SET @IsAuthorisedCreator = 2;
        SELECT @IsBulkUploadAuthorized = IsBulkUpload FROM #TempCreditChangeApproverMapping
    END
    ELSE IF EXISTS (SELECT 1 FROM #TempCreditChangeApproverMapping WHERE ApproverType = 2)
    BEGIN
        SET @IsAuthorisedCreator = 3;

        DECLARE @RM4ProductId INT, @RM4FloorProcess VARCHAR(255)
        SELECT
            @RM4FloorProcess = TEMP.FloorProcess,@RM4ProductId = TEMP.ProductID
            FROM #TempCreditChangeApproverMapping TEMP

            INSERT INTO #TempUserIDforRM4(UserID)
            SELECT DISTINCT UD.UserID
            FROM MTX.AgentProcessDetails APD
            INNER JOIN CRM.UserDetails UD on UD.EmployeeId = APD.AMEmployeeId
            WHERE APD.ProductId IN (7) AND APD.IncentiveMonth >= dateadd(month, -3, @MaxIncentiveMonth)
            INSERT INTO #TempUserIDforRM4(UserID)
            SELECT DISTINCT UD.UserID
            FROM MTX.AgentProcessDetails APD
            INNER JOIN CRM.UserDetails UD on UD.EmployeeId = APD.ManagerEmployeeID
            WHERE APD.ProductId IN (7) AND APD.IncentiveMonth >= dateadd(month, -3, @MaxIncentiveMonth)
    END
    ELSE
    BEGIN
        -- Fetch current employee ID and max incentive month
        SELECT 
            @CurrentEmployeeID = EMPLOYEEID
            FROM CRM.UserDetails
            WHERE UserID = @UserID;

				INSERT INTO #TempAgentProcessDetails(AgentID,AMEmployeeID,TLEmployeeID,ManagerEmployeeId,ProductID,IncentiveMonth,FloorProcess)
				SELECT AgentID,AMEmployeeID,TLEmployeeID,ManagerEmployeeId,ProductID,IncentiveMonth,FloorProcess
				FROM  MTX.AgentProcessDetails 
                WHERE 
                    (AMEmployeeId = @CurrentEmployeeID 
                    OR TLEmployeeId = @CurrentEmployeeID
                    OR ManagerEmployeeId = @CurrentEmployeeID) 
                AND ISACTIVE = 1
				AND IncentiveMonth >= @MaxIncentiveMonth
                AND ProductId IN (7)

				IF EXISTS (SELECT TOP 1 1 FROM #TempAgentProcessDetails WHERE (AMEmployeeId = @CurrentEmployeeID OR ManagerEmployeeId = @CurrentEmployeeID))
					BEGIN
						SET @IsAuthorisedCreator = 1;
					END
    END

    IF(@IsAuthorisedCreator = 3)
    BEGIN 
        SELECT DISTINCT
	    	CCR.RequestID,
            CCR.[BookingId],
            LD.CustomerID,
            LD.ProductID,
            CCR.AgentType,
            CCR.AgentTypeID,
            CCR.[RequestedByUserID],
            UD.EmployeeId AS RequestedByEmpCode,
            UD.UserName AS RequestedByUserName,
            CCR.[OldAdvisorUserID],
            UD1.EmployeeId AS OldAdvisorEmpCode,
            UD1.UserName AS OldAdvisorUserName,
            CCR.[NewAdvisorUserID],
            UD2.EmployeeId AS NewAdvisorEmpCode,
            UD2.UserName AS NewAdvisorUserName,
            CCR.[FirstLevelApproverUserID],
            UD3.EmployeeId AS FirstLevelApproverEmpCode,
            UD3.UserName AS FirstLevelApproverUserName,
            CCR.[ApprovedByUserID],
            CCR.StatusID,
            CCSM.Status AS [Status],
            CCR.Createdon,
            CCR.[Updatedon],
            CCR.ReasonID,
            RM.Reason,
            BD.OfferCreatedON AS BookingDate,
            CCR.RequestorComment,
            CCR.FirstLevelComment,
            CCR.SecondLevelComment,
            CCR.ReferenceId,
            LDRef.CustomerID AS ReferenceCustId,
            LDRef.ProductID AS ReferenceProdId,
            UD4.UserName AS ApprovedByUserName,
            UD4.EmployeeId AS ApprovedByEmpCode
        FROM MTX.CreditChangeRequests CCR
        INNER JOIN MTX.CreditChangeReasonMaster RM
            ON RM.ReasonID = CCR.ReasonID
        INNER JOIN MTX.CreditChangeStatusMaster CCSM
            ON CCSM.StatusID = CCR.StatusID
        INNER JOIN MATRIX.MTX.BookingDetails BD (NOLOCK)
            ON BD.LEADID = CCR.BookingId
        INNER JOIN MATRIX.CRM.LeadDetails LD (NOLOCK)
            ON LD.LEADID = CCR.BookingId
        INNER JOIN CRM.UserDetails UD (NOLOCK)
            ON UD.USERID = CCR.RequestedByUserID
        INNER JOIN CRM.UserDetails UD1 (NOLOCK)
            ON UD1.USERID = CASE WHEN ISNULL(CCR.OldAdvisorUserID,124) IN (124,0) THEN 124 ELSE CCR.OldAdvisorUserID END
        INNER JOIN CRM.UserDetails UD2 (NOLOCK)
            ON UD2.USERID = CCR.NewAdvisorUserID
        INNER JOIN CRM.UserDetails UD3 (NOLOCK)
            ON UD3.USERID = ISNULL(CCR.FirstLevelApproverUserID,124)
        LEFT JOIN MATRIX.CRM.LeadDetails LDRef (NOLOCK)
            ON ISNULL(CCR.ReferenceId,0) = LDRef.LeadID
        LEFT JOIN CRM.UserDetails UD4 (NOLOCK)
            ON UD4.UserID = ISNULL(CCR.ApprovedByUserID, 0)
        INNER JOIN #TempUserIDforRM4 TEMPUSERID
            ON TEMPUSERID.UserID = CCR.RequestedByUserID
        WHERE ((CCR.ISACTIVE = 1) OR (CCR.IsActive = 0 AND CCR.Createdon > GETDATE() - 90 AND CCR.StatusID NOT IN (6)))
            AND ISNULL(CCR.BookingId, 0) <> 0
            AND CCR.ProductId = 7
        ORDER BY CCR.CreatedOn DESC;
    END
    ELSE
    BEGIN
        SELECT 
            CCR.[BookingId],
	    	CCR.RequestID,
            LD.CustomerID,
            LD.ProductID,
            CCR.AgentType,
            CCR.AgentTypeID,
            CCR.[RequestedByUserID],
            UD.EmployeeId AS RequestedByEmpCode,
            UD.UserName AS RequestedByUserName,
            CCR.[OldAdvisorUserID],
            UD1.EmployeeId AS OldAdvisorEmpCode,
            UD1.UserName AS OldAdvisorUserName,
            CCR.[NewAdvisorUserID],
            UD2.EmployeeId AS NewAdvisorEmpCode,
            UD2.UserName AS NewAdvisorUserName,
            CCR.[FirstLevelApproverUserID],
            UD3.EmployeeId AS FirstLevelApproverEmpCode,
            UD3.UserName AS FirstLevelApproverUserName,
            CCR.[ApprovedByUserID],
            CCR.StatusID,
            CCSM.Status AS [Status],
            CCR.Createdon,
            CCR.[Updatedon],
            CCR.ReasonID,
            RM.Reason,
            BD.OfferCreatedON AS BookingDate,
            CCR.RequestorComment,
            CCR.FirstLevelComment,
            CCR.SecondLevelComment,
            CCR.ReferenceId,
            LDRef.CustomerID AS ReferenceCustId,
            LDRef.ProductID AS ReferenceProdId,
            UD4.UserName AS ApprovedByUserName,
            UD4.EmployeeId AS ApprovedByEmpCode
        FROM MTX.CreditChangeRequests CCR
        INNER JOIN MTX.CreditChangeReasonMaster RM
            ON RM.ReasonID = CCR.ReasonID
        INNER JOIN MTX.CreditChangeStatusMaster CCSM
            ON CCSM.StatusID = CCR.StatusID
        INNER JOIN MATRIX.MTX.BookingDetails BD (NOLOCK)
            ON BD.LEADID = CCR.BookingId
        INNER JOIN MATRIX.CRM.LeadDetails LD (NOLOCK)
            ON LD.LEADID = CCR.BookingId
        INNER JOIN CRM.UserDetails UD (NOLOCK)
            ON UD.USERID = CCR.RequestedByUserID
        INNER JOIN CRM.UserDetails UD1 (NOLOCK)
            ON UD1.USERID = CASE WHEN ISNULL(CCR.OldAdvisorUserID,124) IN (124,0) THEN 124 ELSE CCR.OldAdvisorUserID END
        INNER JOIN CRM.UserDetails UD2 (NOLOCK)
            ON UD2.USERID = CCR.NewAdvisorUserID
        INNER JOIN CRM.UserDetails UD3 (NOLOCK)
            ON UD3.USERID = ISNULL(CCR.FirstLevelApproverUserID,124)
        LEFT JOIN MATRIX.CRM.LeadDetails LDRef (NOLOCK)
            ON ISNULL(CCR.ReferenceId,0) = LDRef.LeadID
        LEFT JOIN CRM.UserDetails UD4 (NOLOCK)
            ON UD4.UserID = ISNULL(CCR.ApprovedByUserID, 0)
        WHERE ((CCR.ISACTIVE = 1) OR (CCR.IsActive = 0 AND CCR.Createdon > GETDATE() - 75 AND CCR.StatusID NOT IN (6)))
            AND ISNULL(CCR.BookingId, 0) <> 0
            AND (
                (@IsAuthorisedCreator = 2 AND CCR.StatusID IN(3,4,5) AND
                    (CCR.RequestedByUserID IN (SELECT UserID FROM #TermUsers))
                    AND CCR.AgentTypeID IN (SELECT AgentType FROM #TempCreditChangeApproverMapping))
                OR
                (   (@IsAuthorisedCreator = 1 OR @IsAuthorisedCreator = 0) AND
                    EXISTS (
                        SELECT 1
                        FROM #TempAgentProcessDetails APD
                        WHERE (
	    						((APD.AgentId = OldAdvisorUserID AND (APD.TLEmployeeId = @CurrentEmployeeID OR APD.AMEmployeeID = @CurrentEmployeeID OR APD.ManagerEmployeeID = @CurrentEmployeeID))
	    						OR (APD.AgentId = NewAdvisorUserID AND (APD.TLEmployeeId = @CurrentEmployeeID OR APD.AMEmployeeID = @CurrentEmployeeID OR APD.ManagerEmployeeID = @CurrentEmployeeID))
	    						OR RequestedByUserID = @UserID 
                                OR FirstLevelApproverUserID = @UserID
                                )
	    					  )
	    						AND 
                            (
                                (APD.IncentiveMonth >= @MaxIncentiveMonth AND APD.ProductId = 7) 
                            )
                    )
                )
                )
                AND CCR.ProductId = 7
        ORDER BY CCR.CreatedOn DESC;
    END

	SELECT @IsAuthorisedCreator AS IsAuthorisedCreator, @IsBulkUploadAuthorized AS IsBulkUploadAuthorized;
	DROP TABLE IF EXISTS #TempCreditChangeApproverMapping;
    DROP TABLE IF EXISTS #TempAgentProcessDetails;
	DROP TABLE IF EXISTS #TermUsers;
	DROP TABLE IF EXISTS #TempUserIDforRM4;
END