-- =============================================
-- Author:		<PERSON><PERSON>
-- Create date: 21-11-2024
-- Description:	Park15UnansweredAttemptLeads
-- =============================================

CREATE PROCEDURE [MTX].[Park15UnansweredAttemptLeads]
AS
BEGIN    
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    SET DEADLOCK_PRIORITY LOW
    SET NOCOUNT ON;

    CREATE TABLE #LeadTable
    (
          ID INT IDENTITY(1,1), LeadId BIGINT,MobileNo VARCHAR(50),LeadRank SMALLINT,Createdon DATETIME,CustId BIGINT,StatusId TINYINT,SubStatusId SMALLINT,                  
          IsAllocable BIT ,policyexpirydate datetime,GroupID SMALLINT,AssignedUser BIGINT,Attempts SMALLINT, <PERSON><PERSON><PERSON><PERSON> BIT,JobID SMALLINT,ItemId SMALLINT,
          ProductID SMALLINT,AssignID BIGINT, NotChurnReason VARCHAR(100), LogEntry BIT
    )       

    /*Customer Blocked lead keep unassign*/
    INSERT INTO #LeadTable
    (
        LeadId,LeadRank,Createdon,CustId,StatusId,SubStatusId,IsAllocable,policyexpirydate,GroupID,Attempts,JobID,ItemId,ProductID,LogEntry
    )
    SELECT      DISTINCT LAD.LeadID,LAD.Leadrank, LAD.CreatedON,LD.CustomerID,1,0,1
                ,GETDATE() AS PreviousPolicyExpiryDate
                ,3388
                ,0,91,LAD.AssignToGroupId,LD.ProductID, 1
    FROM        [Matrix].[CRM].[LeadAssignDetails](NOLOCK) LAD     
    INNER JOIN  [Matrix].[CRM].[leaddetails150] LD (NOLOCK) ON LD.LeadID=LAD.LeadID 
    AND         LD.ProductID IN (2, 7, 115,117,3) 
    AND			LD.LeadSource <> 'RENEWAL'
    AND         LD.ParentID IS NULL
    INNER JOIN  CRM.LeadStatus(NOLOCK) LS ON LAD.LeadID=LS.LeadID AND LS.IsLastStatus=1 AND LS.StatusID IN (1,2,3,4,11)        
    INNER JOIN  Matrix.mtx.CustomerUnsubscription CUS (NOLOCK) ON LD.CustomerID=CUS.CustomerId     
    AND         CUS.CategoryId=1 AND CUS.ChannelId=3 AND CUS.IsActive=1 AND  CUS.ProductId = LD.ProductID       
    --AND         CUS.CreatedOn > LAD.CreatedOn
    WHERE       LAD.CreatedOn BETWEEN CAST(GETDATE()-90 AS DATE) AND GETDATE()
    AND         IsLastAssigned=1 AND LAD.AssignedToUserID > 0   

    UPDATE      LT
    SET         LT.IsAllocable=0
    FROM        #LeadTable LT
    INNER JOIN  [Matrix].[CRM].[leaddetails150] LD ON LD.ParentID=LT.LeadId AND LD.LeadSource='RENEWAL'
    

    /*keep unassign if attempts more than 15 UA*/
    INSERT INTO #LeadTable
    (
        LeadId,LeadRank,Createdon,CustId,StatusId,SubStatusId,IsAllocable,policyexpirydate,GroupID,Attempts,JobID,ItemId,ProductID,LogEntry
    )
    SELECT      LAD.LeadID,LAD.Leadrank, MIN(LAD.CreatedON),LD.CustomerID,1,0,1
                ,GETDATE() AS PreviousPolicyExpiryDate
                ,3387
                ,0,91,LAD.AssignToGroupId,LD.ProductID, 1
    FROM        [Matrix].[CRM].[LeadAssignDetails](NOLOCK) LAD     
    INNER JOIN  [Matrix].[CRM].[leaddetails150] LD (NOLOCK) ON LD.LeadID=LAD.LeadID 
    AND         LD.ProductID IN (2, 7, 115) AND LeadSource NOT IN ('RENEWAL','inbound') 
    AND         ISNULL(Utm_source,'') NOT IN ('HOM_RelHI')
    AND         LD.ParentID IS NULL
    INNER JOIN  CRM.LeadStatus(NOLOCK) LS ON LAD.LeadID=LS.LeadID AND LS.IsLastStatus=1 AND LS.StatusID IN (1,2,3,4,11)
    INNER JOIN  MTX.CallDataHistory(NOLOCK) CDH ON LAD.LeadID=CDH.LeadID AND CDH.Duration > 2 AND  CDH.Context <> 'TWOWAYCALL' AND CDH.CreatedOn > GETDATE()-150     
    LEFT  JOIN  #LeadTable LT ON LD.LeadID=LT.LeadId
    WHERE       LD.CreatedOn BETWEEN CAST(GETDATE()-120 AS DATE) AND CAST(GETDATE()-7 AS DATE)    
    AND         LT.LeadId IS NULL
    AND         IsLastAssigned=1 AND LAD.AssignedToUserID > 0    
    AND         ISNULL(LD.Country,'') IN ('91','392','0','others','india','') 
    GROUP BY    LAD.LeadID,LAD.Leadrank,LD.CustomerID,LAD.AssignToGroupId,LD.ProductID
    HAVING      SUM(CASE WHEN talktime=0 AND Duration > 2 AND CDH.Context <> 'TWOWAYCALL' THEN 1 ELSE 0 END) >= 15 
                AND SUM(talktime) < 60     

    /*keep unassign if attempts more than 10 UA*/
    INSERT INTO #LeadTable
    (
        LeadId,LeadRank,Createdon,CustId,StatusId,SubStatusId,IsAllocable,policyexpirydate,GroupID,Attempts,JobID,ItemId,ProductID,LogEntry
    )
    SELECT      LAD.LeadID,LAD.Leadrank, MIN(LAD.CreatedON),LD.CustomerID,1,0,1
                ,GETDATE() AS PreviousPolicyExpiryDate
                ,3387
                ,0,104,LAD.AssignToGroupId,LD.ProductID, 1
    FROM        [Matrix].[CRM].[LeadAssignDetails](NOLOCK) LAD     
    INNER JOIN  [Matrix].[CRM].[leaddetails150] LD (NOLOCK) ON LD.LeadID=LAD.LeadID 
    AND         LD.ProductID IN (7) AND LeadSource NOT IN ('RENEWAL')     
    AND         LD.ParentID IS NULL
    INNER JOIN  CRM.LeadStatus(NOLOCK) LS ON LAD.LeadID=LS.LeadID AND LS.IsLastStatus=1 AND LS.StatusID IN (1,2,3,4,11)
    INNER JOIN  MTX.CallDataHistory(NOLOCK) CDH ON LAD.LeadID=CDH.LeadID AND CDH.Duration > 2 AND  CDH.Context <> 'TWOWAYCALL' AND CDH.CreatedOn > GETDATE()-120 
    LEFT  JOIN  CRM.EventDetails (NOLOCK) ED on ED.LeadId=LAD.LeadID
    LEFT  JOIN  #LeadTable LT ON LD.LeadID=LT.LeadId
    WHERE       LD.CreatedOn BETWEEN CAST(GETDATE()-120 AS DATE) AND CAST(GETDATE()-2 AS DATE)
    AND         ED.EventID is NULL
    AND         LT.LeadId IS NULL
    AND         IsLastAssigned=1 AND LAD.AssignedToUserID > 0   
    AND         LAD.Leadrank IN (10 ,13 ,14 ,15 ,16 ,17 ,18 ,28 ,53 ,54 ,56 ,58 ,74 ,89 ,90 ,99 ,104 ,108 ,118 ,128 ,129 ,134 ,135 ,168 ,178 ,187 ,193 ,194 ,197 ,199 ,204 ,207 ,208 ,213 ,217 ,234 ,238 ,245 ,248 ,252 ,273 ,274 ,276 ,309 ,320 ,325 ,342 ,347 ,352 ,357 ,380 ,388 ,402 ,403 ,404 ,406 ,408 ,409 ,418 ,420 ,422 ,423 ,426 ,437 ,438 ,439 ,440 ,455 ,456 ,457 ,458 ,459 ,475 ,476 ,485 ,491 ,492 ,494 ,495 ,509 ,520 ,538 ,539 ,553 ,613 ,614 ,615 ,621 ,622 ,630 ,634 ,653 ,654 ,655 ,684) 
    AND         ISNULL(LD.Country,'') IN ('91','392','0','others','india','') 
    GROUP BY    LAD.LeadID,LAD.Leadrank,LD.CustomerID,LAD.AssignToGroupId,LD.ProductID
    HAVING      SUM(CASE WHEN talktime=0 AND Duration > 2 AND CDH.Context <> 'TWOWAYCALL' THEN 1 ELSE 0 END) >= 10 
                AND SUM(talktime) < 60   
                AND MIN(LAD.CreatedON) < CAST(GETDATE()-1 AS DATE)              
    
    /*Do not churn any lead if appointment is created*/
    UPDATE      LT
    SET         LT.IsAllocable=0, LogEntry=0, LT.NotChurnReason='Appointment'
    FROM        #LeadTable LT 
    INNER JOIN  MTX.AppointmentData  AD (NOLOCK) ON AD.LeadID=LT.LeadID AND AD.IsActive=1        

    UPDATE      LT
    SET         LT.IsAllocable=0, LogEntry=0, LT.NotChurnReason='Booking'
    FROM        [Matrix].[CRM].[Leaddetails] LD (NOLOCK)
    INNER JOIN  #LeadTable LT ON LD.ParentID=LT.LeadId
    INNER JOIN  Matrix.MTX.BookingDetails BD (NOLOCK) ON LD.LeadID=BD.LEADID AND BD.PaymentSTATUS IN (300,3002,4002,5002,6002)

    UPDATE      LT
    SET         LT.IsAllocable=0, LogEntry=0, LT.NotChurnReason='Booking'
    FROM        #LeadTable LT 
    INNER JOIN  Matrix.MTX.BookingDetails BD (NOLOCK) ON LT.LeadID=BD.LEADID AND BD.PaymentSTATUS IN (300,3002,4002,5002,6002)     
    
    -- A/B testing 50%
    UPDATE      LT
    SET         LT.IsAllocable=0, LT.NotChurnReason='ControlGroup'
    FROM        #LeadTable LT 
    WHERE       LT.LeadId % 2 = 0
    AND         LT.GroupID =3387
    AND         LT.IsAllocable=1
    AND         ISNULL(LT.JobID,0) <> 104    

    UPDATE      LT
    SET         LT.IsAllocable=1, LT.NotChurnReason=NULL
    FROM        #LeadTable LT 
    WHERE       LT.Createdon > '2025-05-30'
    AND         LT.GroupID =3387
    AND         LT.NotChurnReason='ControlGroup' 
    AND         LT.IsAllocable=0
    
        
---------------------------------------------------------------------------------------------------------------------------------------------
----------------------------------- Start allocating leads ------------------------------------------------------
    DECLARE @TotalLeadCount BIGINT=0        
    DECLARE @Counter BIGINT=1        
    DECLARE @LeadId BIGINT          
    DECLARE @LeadRank SMALLINT      
    Declare @InsurerId int
    DECLARE @CustID BIGINT 
    DECLARE @RenewalYear INT
    DECLARE @UserID BIGINT
    DECLARE @AssignedGroupId SmallInt = 0
    Declare @AgentGrade Int
    Declare @LeadStatusId TinyInt
    Declare @LeadSubStatusId SmallInt
    DECLARE @IsAllocable BIT=0
    DECLARE @PolicyExpiryDate DATE=null
    DECLARE @PreviousAssignedUser BIGINT
    DECLARE @Attempts SMALLINT=0
    DECLARE @JobID SMALLINT=0
    DECLARE @NotChurnReason VARCHAR(100)=''
    DECLARE @LogEntry BIT=0
    

    SELECT @TotalLeadCount = COUNT(1) FROM #LeadTable 
    IF @TotalLeadCount>0         
     BEGIN                        
          WHILE @Counter <= @TotalLeadCount        
              BEGIN             
                        SET @IsAllocable=1      
                        SET @UserID=null            
                        SET @PolicyExpiryDate=null  
                        SET @AssignedGroupId=0      
                        SELECT  @LeadId = LeadId,@LeadRank = LeadRank,@CustID = CustId,@LeadStatusId = StatusId,@LeadSubStatusId =SubStatusId
                        ,@IsAllocable=IsAllocable,@policyexpirydate=CAST(policyexpirydate AS DATE),@AssignedGroupId=GroupID
                        ,@PreviousAssignedUser=AssignedUser,@Attempts=Attempts,@JobID=JobID, @NotChurnReason=NotChurnReason,
                        @LogEntry=LogEntry
                        FROM    #LeadTable WHERE ID = @Counter          

                        IF @IsAllocable=1
                            BEGIN                       
                                SET @UserID=null
                                EXEC [CRM].[Insert_AssignedToAgent] @UserID ,124,2,@LeadId,@AssignedGroupId ,1,@JobID,@InsurerId,0,@LeadRank
                            END 
                        
                        -- log 
                        /*IF @LogEntry=1 AND @AssignedGroupId=3387
                            BEGIN
                                INSERT INTO [MTX].[LeadChurnLogs](LeadId,GroupId,IsChurned,NotChurnReason,JobName,Userid,CreatedOn)
                                VALUES(@LeadId,@AssignedGroupId,@IsAllocable,@NotChurnReason,'Park15UnansweredAttemptLeads',124, GETDATE())
                            END*/
                        SET @Counter=@Counter+1                                                 
              END                                             
    END         
	
END
