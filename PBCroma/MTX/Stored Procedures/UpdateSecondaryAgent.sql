CREATE PROCEDURE [MTX].[UpdateSecondaryAgent]   
AS      
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED  
SET DEADLOCK_PRIORITY LOW  
SET NOCOUNT ON  
BEGIN                  
				DECLARE @temp TABLE(leadid BIGINT,OfferCreatedOn DATETIME,ProductId SMALLINT)  
				INSERT  INTO @temp  
				SELECT  DISTINCT A.LEADID,<PERSON><PERSON>,A.ProductId   
				FROM    [Matrix].[MTX].[BookingDetails] (nolock) A  
				WHERE   A.PaymentSTATUS IN (300,4002,5002,3002,6002) AND A.ProductID IN (2,7,115,117,139,130)  
				AND     OfferCreatedON>=DATEADD(MINUTE,-8,GETDATE()) 
				AND OfferCreatedON<=DATEADD(MINUTE,-1,GETDATE()) 
				ORDER BY A.ProductID ASC

        IF DATEPART(HOUR,GETDATE())=22 AND DATEPART(MINUTE,GETDATE()) BETWEEN 20 AND 22
            BEGIN
                INSERT  INTO @temp  
                SELECT		DISTINCT A.LEADID,<PERSON><PERSON>,A.ProductId   
                FROM        [Matrix].[MTX].[BookingDetails] (nolock) A  
                INNER JOIN  Matrix.CRM.leaddetails150 LD (NOLOCK) ON A.LEADID=LD.LeadID
                INNER JOIN  MTX.CallDataHistory CDH (NOLOCK) ON 
				CDh.LeadID=CASE WHEN LD.ParentID > 0 THEN LD.ParentID ELSE LD.LeadID END
                AND         CDH.ProductID=7 
                AND         TransferType IN ('regional_transfer-1','regional_transfer','transfertosradvisor')
                WHERE       A.PaymentSTATUS IN (300,4002,5002,3002,6002) AND A.ProductID IN (7)  
                AND         A.OfferCreatedON BETWEEN CAST(GETDATE() AS DATE) AND DATEADD(MINUTE,-9,GETDATE()) 
            END 
    
  
        DECLARE @leadid BIGINT,@ParentId BIGINT,@SecondaryAgentFOS INT=0,@OfferCreatedOn DATETIME  
        DECLARE @SalesAgent INT,@ProductId SMALLINT,@SecondaryAgentPOD int=0,@Utm_Source VARCHAR(255),
		@utm_content VARCHAR(255),@LeadSource VARCHAR(255),@ReferralId BIGINT,@AddOnParentId BIGINT;  
        DECLARE @appCreatedOn datetime;  
  
        WHILE EXISTS(SELECT TOP 1 leadid FROM @temp)  
            BEGIN   
                SET @leadid=NULL  
                SET @OfferCreatedOn=NULL  
                SET @ProductId=NULL  
                SET @ParentId=NULL  
                SET @SecondaryAgentFOS=NULL  
                SET @SecondaryAgentPOD=NULL  
                SET @SalesAgent=NULL  
                SET @Utm_Source=''  
                SET @ReferralId=0;  
                SET @appCreatedOn=NULL;  
                SELECT TOP 1 @leadid=leadid,@OfferCreatedOn=OfferCreatedOn,@ProductId=ProductId FROM @temp                  
  
                DELETE FROM @temp WHERE leadid=@leadid          
  
                SELECT  @ParentId=L.ParentID,@Utm_Source=Utm_source,@ReferralId=[ReferralID],
				@LeadSource=LeadSource,@utm_content=utm_content,@AddOnParentId=AddOnParentID   
                FROM    [Matrix].[CRM].[leaddetails150] (NOLOCK) L WHERE L.LeadID=@leadid;
				
				IF(@ProductId=130)
					BEGIN
					IF(@AddOnParentId>'')
					BEGIN
						EXEC [MTX].[UpdateSecondaryAgentOnSTU]
							@leadID=@leadid,
							@ParentId=@ParentId,
							@productId=@ProductId,
							@OfferCreatedOn=@OfferCreatedOn,
							@AddOnParentId=@AddOnParentId;
                     END  
                     CONTINUE;/*Move to next record*/
					END
               
                /*to manage number change scnario*/
                IF (ISNULL(@LeadSource,'') <> 'Referral' AND @ParentId > 0)  
                    SELECT  @Utm_Source=Utm_source,@ReferralId=[ReferralID],@LeadSource=LeadSource,
					@utm_content=utm_content,@AddOnParentId=AddOnParentID   
                    FROM    [Matrix].[CRM].[leaddetails150] (NOLOCK) L WHERE L.LeadID=@ParentId  
                  
                IF ISNULL(@ParentId,0)=0  
                    SET @ParentId=@leadid

                /*create visit on house wife leads if visit there on parent lead*/
                IF @AddOnParentId > 0 AND @utm_content='same_agent'
                    BEGIN                        
                        SELECT @AddOnParentId= ISNULL(ParentID,LeadID) FROM Matrix.CRM.leaddetails150 (NOLOCK) 
						WHERE LeadID=@AddOnParentId                        
                        EXEC [FOS].[SetFOSAppointmentForHouseWife]@AddOnParentId,@ParentId
                    END          
  
                SELECT TOP 1 @SecondaryAgentFOS=ISNULL(CreatedBy,0),@appCreatedOn=X.CreatedOn   
                FROM    MTX.AppointmentData (NOLOCK) X   
                WHERE   X.CreatedBy>0 AND X.CreatedOn>getdate()-120  
                AND     X.leadid= CASE WHEN @ReferralId > 0 AND 
				@LeadSource='Referral' AND @Utm_Source='FOS_NumberChange' THEN @ReferralId ELSE @ParentId END
				ORDER BY ID ASC                  
  
                SELECT @SalesAgent=SalesAgent FROM [Matrix].[MTX].[AdditionalBookingDetails]
				(NOLOCK) WHERE leadid=@leadid

                IF @SalesAgent > 0 AND @ProductId=7 AND EXISTS(SELECT 1 FROM CRM.UserGroupRoleMapNew WHERE UserId=@SalesAgent AND GroupId IN (1228,739,709,708,42,2919,2920,2921) AND RoleSuperId=11) AND NOT EXISTS(SELECT 1 FROM [MTX].[LimitUpdateAfterBooking] WHERE LeadID=@leadid)
                    BEGIN
                        UPDATE CRM.UserDetails SET Limit=Limit + 3 WHERE UserID=@SalesAgent AND Limit > 0
                        INSERT INTO [MTX].[LimitUpdateAfterBooking] VALUES(@leadid,GETDATE())
                    END  
					
		     
			   IF @SalesAgent > 0 AND EXISTS ( SELECT      1  
                                                FROM        CRM.UserGroupRoleMapNew UGRN (NOLOCK)  
                                                INNER JOIN  MTX.ProductGrpMapping_Allocation PGRMA 
												ON UGRN.GroupId=PGRMA.GroupID AND PGRMA.ProcessID IN (6,7,8)  
                                                WHERE       UGRN.UserId=@SalesAgent  
                                              )  
                    BEGIN              
                        IF(@ProductId=2  AND @LeadSource='Renewal' AND @SecondaryAgentFOS>0)  
                            BEGIN  
                                    SELECT  TOP(1) @SecondaryAgentFOS=AssignedToUserID from
									Matrix.CRM.LeadAssignDetails lad with(nolock)  where   
                                    LeadID=@ParentId and CreatedOn<=@appCreatedOn and AssignedToUserID >''  
                                    and [MTX].[IsHealthRenewalUser](lad.AssignedToUserID)=1 
									and lad.AssignedToUserID!=@SalesAgent  
                                    ORDER BY ID DESC;  
                            END                    
                
                        IF @SecondaryAgentFOS > 0  AND @SecondaryAgentFOS!=@SalesAgent   
                           AND NOT EXISTS(
						             SELECT TOP 1 leadid FROM MTX.SecondaryUserLeadCredit   
                                     WHERE leadid=@leadid AND (Category = 'FOS' OR SecondaryUserID=@SecondaryAgentFOS)) 
                                BEGIN    
                                    INSERT INTO MTX.SecondaryUserLeadCredit  
                                    (SecondaryUserID,leadid,CreatedOn,CreatedBy,ParentLeadId,Category,OriginalSalesAgent,BookingDate,ProductId)  
                                    SELECT @SecondaryAgentFOS,@leadid,getdate(),1,@ParentId,'FOS',@SalesAgent,@OfferCreatedOn,@ProductId                                 
                                END              
                    END

                /*Update secondary booking for transfer cases*/
				IF @ProductId=7
					BEGIN
						DECLARE @TransferAgentID BIGINT=0
						DECLARE @CallID VARCHAR(100) ,@CallDate DATETIME               

						SELECT TOP 1 @CallID = CallID,@CallDate=CallDate FROM MTX.CallDataHistory (NOLOCK) 
						WHERE LeadID= @ParentId AND ProductID=7 
						 AND TransferType IN ('regional_transfer-1','regional_transfer','transfertosradvisor') 
						 ORDER BY CallDataID

						IF ISNULL(@CallID,'') <> '' AND @CallDate IS NOT NULL
						BEGIN
							SELECT TOP 1 @TransferAgentID = UserID FROM MTX.CallDataHistory (NOLOCK) 
							WHERE LeadID= @ParentId AND ProductID=7 AND UserID > 0 
							AND CallID =@CallID AND CallDate < @CallDate 
							ORDER BY CallDataID    
						END	

						IF @SalesAgent > 0 AND @TransferAgentID > 0 AND @TransferAgentID <>  @SalesAgent
							BEGIN
							IF NOT EXISTS(SELECT TOP 1 leadid FROM MTX.SecondaryUserLeadCredit   
								WHERE leadid=@leadid AND (Category = 'TRANSFER' OR SecondaryUserID=@TransferAgentID)) 
								BEGIN
									INSERT INTO MTX.SecondaryUserLeadCredit  
										(SecondaryUserID,leadid,CreatedOn,CreatedBy,ParentLeadId,Category,OriginalSalesAgent,BookingDate,ProductId)  
										SELECT @TransferAgentID,@leadid,getdate(),1,@ParentId,'TRANSFER',@SalesAgent,@OfferCreatedOn,@ProductId                                 
								END                                            
							END                               
					END

          END /*END OF WHILE LOOP*/ 
  
END  
	
