SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Author:		<PERSON>mi
-- Create date: 13 May 2015
-- Description:	Get Contex Queue
-- =============================================
--
--declare @RESULT VARCHAR(50) ;exec [MTX].[GetContexQueue] 'PW52765',@RESULT;select @RESULT

ALTER PROCEDURE [MTX].[GetContexQueue] 
( 
@EmployeeId VARCHAR(50), 
@RESULT VARCHAR(50) OUTPUT,
@ProductID SMALLINT = 0,
@LeadSource VARCHAR(50) = '',
@StateID SMALLINT = 0
) 
AS 
BEGIN
	SET DEADLOCK_Priority LOW
	SET NOCOUNT ON;
	--DECLARE @userDetails TABLE
	--(
	--	GroupId SMALLINT not null primary key
	--)
	--SET @RESULT = '0'
	
	--INSERT INTO @userDetails
	--SELECT distinct UGRMN.GroupId 
	--FROM CRM.UserGroupRoleMapNew UGRMN WITH(NOLOCK) 
	--INNER JOIN CRM.UserDetails UD WITH(NOLOCK) ON UD.UserID = UGRMN.UserId AND UD.EmployeeId = @EmployeeId AND UD.IsActive = 1
	
	--SELECT @RESULT=Contex FROM CRM.UserGroupMaster WITH(NOLOCK) WHERE UserGroupID IN 
	--(SELECT GroupId FROM @userDetails ) AND Contex IS NOT NULL 
	

	DECLARE @IP AS VARCHAR(25)
	DECLARE @DID AS VARCHAR(50)
	DECLARE @CallingCompany AS VARCHAR(30)
	DECLARE @VirtualNo AS VARCHAR(15)
    DECLARE @GroupID SMALLINT
	DECLARE @isWFH BIT
	DECLARE @AgentCountry SMALLINT
    DECLARE @AswatPosition VARCHAR(10)
	
	SELECT TOP 1 @RESULT=Contex
	--,@IP=CASE WHEN UGRMN.RoleSuperId=11 AND ugm.ProcessID IN (2,5) AND ISNULL(ugm.Asterisk_IP,'') <> '' THEN ugm.Asterisk_IP ELSE UD.Asterisk_IP END 
	,@IP=CASE WHEN UGRMN.RoleSuperId IN (10,11,15,19,38) AND ISNULL(ugm.Asterisk_IP,'') <> '' THEN ugm.Asterisk_IP WHEN ISNULL(UD.Asterisk_IP,'') <> '' THEN UD.Asterisk_IP ELSE '************' END 
	,@DID=DIDNo
    ,@AswatPosition=CASE   WHEN LEN( UD.Aswatposition)=3 THEN '0' + CAST( UD.Aswatposition AS VARCHAR)
                                WHEN LEN( UD.Aswatposition)=2 THEN '00' + CAST( UD.Aswatposition AS VARCHAR)
                                WHEN LEN( UD.Aswatposition)=1 THEN '000' + CAST( UD.Aswatposition AS VARCHAR) 
                                                          ELSE CAST(UD.Aswatposition AS VARCHAR) END                    
    ,@CallingCompany= UD.CallingCompany
    ,@GroupID=UGRMN.GroupId,@VirtualNo=UD.VirtualNo 
	,@isWFH=ISNULL(UD.IsWFH,0)
	,@AgentCountry=AgentCountry
	FROM CRM.UserGroupRoleMapNew UGRMN WITH(NOLOCK) 
	INNER JOIN CRM.UserDetails UD WITH(NOLOCK) 
	ON UD.UserID = UGRMN.UserId AND UD.EmployeeId = @EmployeeId AND UD.IsActive = 1
	INNER JOIN crm.UserGroupMaster ugm with(nolock) 
	ON UGRMN.GroupId=ugm.UserGroupID and ugm.Contex is not null 
	ORDER BY ugm.UserGroupID DESC
	
	-- For TLs in BMS
	If @EmployeeId IN ('BPW12337','BPW00967','BPW08250','BPW13051','BPW00521','BPW00645','BPW03337','BPW01013','BPW01464','BPW00423','BPW01852','BPW02222','BPW02436','BPW00605','BPW01362','BPW02106','BPW04569','BPW07312','BPW00969','BPW01710','BPW11660','BPW11785','BPW11658','BPW01377','BPW07343','BPW15683','BAU00036','BPW12337','BPW00967','BPW13051','BBE01139','BPW17704','BPW20119','BPW09365')
		SET @RESULT='bmshealthcommon'
	else if @EmployeeId IN ('BPW00510','BPW00678','BPW00516','BPW12657','BPW00069','BPW01076','BPW03769','BPW18689')
		SET @RESULT='bmsmotor'
	else if @EmployeeId IN ('BPW01304','BPW00960','BPW05141','BPW10429','BPW00590','BPW03625','BPW00960','BPW05141','BPW10429','BPW00590','BPW03625','BPW16797','BPW15051','BPW13868','BPW08474','BPW24032','BPW04444','BPW05804')
		SET @RESULT='bmsterm'
    else 
        SELECT @RESULT = Contex from MTX.StateContextMapping WHERE ProductID = @ProductID AND LeadSource = @LeadSource AND StateID = @StateID AND IsActive = 1;

	SELECT @RESULT AS Context,@IP AS IP
     --,CASE WHEN @CallingCompany='ASWATINDIA' AND @AswatPosition IS NOT NULL THEN  @AswatPosition ELSE  @DID END AS DIDNO
    ,@DID AS DIDNO
    ,@CallingCompany AS CallingCompany, @GroupID AS GroupID,@VirtualNo AS VirtualNo,@isWFH AS iSWFH,ISNULL(@AgentCountry,0) AS AgentCountry,@AswatPosition AS AswatPosition
	
END

GO
