

-- Author:  <PERSON><PERSON><PERSON>   
-- Create date: 28-07-2016  
-- Description: CAR AutoAllocation  
--[MTX].[GetLeadAllocationData_Car]
-- ============================================= 
CREATE PROCEDURE [MTX].[GetLeadAllocationData_Car]
      (
            @FromDate     DATETIME  = NULL,
            @ToDate       DATETIME  = NULL,
            @LeadId       BIGINT    = 0,
			@200flag      BIT		= 0,
			@IsGroup       BIT		= 0
      )
As
SET NOCOUNT ON
SET DEADLOCK_PRIORITY LOW
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
Begin
      
      IF(@FromDate IS NULL OR @ToDate IS NULL)
            BEGIN
                  SET @FromDate = CAST(CAST(GETDATE()-1 AS DATE) AS VARCHAR)+' 10:00:00.000'
                  SET @ToDate   =   GETDATE()
            END	
      
      IF (@LeadId > 0)
			BEGIN						
				SELECT @LeadId = CASE WHEN ParentID>0 THEN ParentID ELSE @LeadID  END	
				FROM   [Matrix].[CRM].[leaddetails150] (NOLOCK) where LeadID = @LeadID 
			END         
	  
	  IF @IsGroup=1
		  BEGIN	
			  SELECT TOP 10000 LD.LeadID,LD.ProductID
							  ,CASE WHEN ld.LeadSource ='MyAcc' THEN 2 
										 WHEN ld.Utm_source='Health RM' THEN 1																			
									ELSE (SELECT [MTX].[GetLeadRank_Car_Allocation](ISNULL(KFA.InsurerID,0),ISNULL(KFA.PreviousBooking,0),CD.RegistrationNo,CD.RegistrationDate,CD.PreviousPolicyExpiryDate,LD.LeadSource,LD.CreatedOn,CD.PolicySubType
                                    ,CASE WHEN CD.PolicyType='NEW' THEN 1 ELSE 0 END,LD.cityID,CD.MakeID,LD.Utm_Source,CD.RegisteredStateId,CD.CarRegisterCityId,CD.ExpectedDeliveryDate)) END LeadRank 
							  ,LD.Utm_campaign,LD.LeadSource,LD.ChatStatus,LD.MKTRevenue as LeadEntryPPL
							  ,ISNULL(LD.LeadRank,0) LeadGrade,LD.StatusID,ISNULL(LD.SubStatusID,0) SubStatusID
							  ,CASE WHEN LD.Source='MOBONLY' AND CD.RegisteredStateId IN (30,26) AND ISNULL(ld.ChatStatus,0) <> 1 THEN 'Tamilan Team' 
									WHEN LD.Source='MOBONLY' AND CD.PolicyType='NEW' AND ISNULL(ld.ChatStatus,0) <> 1 THEN 'New Car'
									WHEN LD.Source='MOBONLY' AND ISNULL(ld.ChatStatus,0) <> 1 THEN 'MOBONLY MOTOR'
                                    --WHEN LD.StateID=11 AND CD.RegistrationDate IS NULL AND DATEDIFF(YEAR,CD.RegistrationDate,CAST(GETDATE() AS DATE)) BETWEEN 5 AND 7 THEN 'Gujarat five-seven'
                                    WHEN LD.StateID=20 AND CD.RegistrationDate IS NULL AND ISNULL(CD.PolicyType,'') <> 'NEW' THEN 'MH five-seven'
									ELSE (SELECT [MTX].[GetSpecialGroup_Allocation](LD.ProductID,LD.LeadSource,LD.Utm_source,LD.UTM_Medium,LD.Utm_campaign,LD.Utm_term
							  ,CASE WHEN LAD.AssignToGroupId=466 THEN 0 ELSE LD.ChatStatus END,0,CD.PreviousPolicyExpiryDate
							  ,CASE WHEN LEN(CD.RegistrationNo) < 8 OR CD.PolicyType='NEW' THEN 1 ELSE 0 END
							  ,CD.MakeId,MV.CubicCapacity
							  ,ISNULL(LD.CityID,0),LD.StateID)) END AS  Groupcode ,0 as LeadTypeId
							  ,CD.RegistrationDate,LD.CreatedON,LD.CustomerID,CD.RegistrationNo,LD.Utm_source,CD.PreviousPolicyExpiryDate
							  ,1 as IsAssist,CASE WHEN LAD.AssignToGroupId IN (466,716,1366,1419) THEN 'REASSIGN' ELSE LD.UTM_Medium END,CD.PolicySubType
                              ,CASE WHEN LAD.AssignToGroupId IN (1419) THEN LAD.AssignToGroupId ELSE NULL END AS GroupID
							  ,CD.MakeId,CD.TentativePurchaseDate,LD.StateID,CD.VehicleVariant
			  FROM            [Matrix].[CRM].[leaddetails150] LD  WITH(NOLOCK)
			  INNER JOIN	  [Matrix].[CRM].[LeadAssignDetails] LAD WITH (NOLOCK) ON LD.LeadId=LAD.LeadID AND LAD.IsLastAssigned=1         			  
			  INNER JOIN	  CRM.CarDetails CD (NOLOCK) on CD.LeadId=LD.LeadID
			  left JOIN		  MTX.KeyFactors_Allocation KFA (NOLOCK) ON LD.LeadId=KFA.LeadID  			 
			  LEFT  JOIN	  MTX.M_Variant MV (NOLOCK) ON CD.VehicleVariant=MV.VariantId  			      
			  LEFT  JOIN	  CRM.InvalidMobileNumbers IMN WITH(NOLOCK) ON IMN.MobileNo=LD.MobileNo 			  
			  WHERE           IMN.id  IS NULL   AND LD.CreatedON BETWEEN @FromDate  AND @ToDate
			  AND			  LD.LeadID=CASE @LeadId WHEN 0 THEN LD.LeadID ELSE @LeadId END	
			  AND			  LD.ProductID = 117       AND  (LD.IsActive=1 OR LD.IsActive  IS NULL) AND  LD.ParentID IS NULL			  
			  AND			  LD.StatusID in (1,2,3,4,11)
			  AND			  (LAD.AssignToGroupId IN (716,1366) AND LAD.AssignedToUserID is null)
			  AND			  (LD.ProductType IS NULL  OR LD.ProductType=1) and ISNULL(LD.Country,'') NOT IN( 'OTHERS','999')			  
			  AND			  ISNULL(LD.leadrank,0) <> 200 	
              AND             LD.CustomerID NOT IN (35061581,19054071,19054048,19054004,35070194,19053976,35063061,35062746)  
			  --AND			  LD.ChatStatus <> 1	
			  ORDER BY		  LD.LeadID DESC      
		  END	  		
	  ELSE IF @LeadId > 0 
		  BEGIN	            
			  SELECT DISTINCT TOP 10000 LD.LeadID,LD.ProductID
							  ,CASE WHEN ld.LeadSource ='MyAcc' THEN 2 
										 WHEN ld.Utm_source='Health RM' THEN 1																			
									ELSE (SELECT [MTX].[GetLeadRank_Car_Allocation](ISNULL(KFA.InsurerID,0),ISNULL(KFA.PreviousBooking,0),CD.RegistrationNo,CD.RegistrationDate,CD.PreviousPolicyExpiryDate,LD.LeadSource,LD.CreatedOn,CD.PolicySubType
                                    ,CASE WHEN CD.PolicyType='NEW' THEN 1 ELSE 0 END,LD.cityID,CD.MakeID,LD.Utm_Source,CD.RegisteredStateId,CD.CarRegisterCityId,CD.ExpectedDeliveryDate)) END LeadRank 
							  ,LD.Utm_campaign,LD.LeadSource,LD.ChatStatus,LD.LeadScore AS LeadEntryPPL
							  ,ISNULL(LD.LeadRank,0) LeadGrade,LD.StatusID,ISNULL(LD.SubStatusID,0) SubStatusID
							  ,CASE --WHEN LD.leadID%30=0 THEN 'Call Centre Unassigned' 
                                    WHEN LD.LeadSource='CrossSell' AND LD.Utm_source='MotorOnCorp' THEN 'Motoroncorp'
                                    WHEN CD.RegistrationNo='23BH2342k' THEN 'UNASSIGN'                                   
                                    WHEN CD.PreviousPolicyExpiryDate IS NOT NULL AND DATEDIFF(DAY,CAST(GETDATE() AS DATE),CD.PreviousPolicyExpiryDate) > 45 THEN '45 days expiry'
                                    WHEN LD.ChatStatus <> 0 AND LD.Utm_source NOT IN ('CRMSMS','CRMPmailer')  THEN 'CallOnChat-Motor'                                                                                                                                                                                  
                                    WHEN LD.LeadSource='Crosssell' AND LD.Utm_source='Health_RM' THEN 'Inbound (Car)'
                                    WHEN LD.LeadSource='whatsapp' AND LD.Utm_source='whatsapp_crm_sales' THEN 'Inbound (Car)'
                                    WHEN CAST(DATEDIFF(DAY,CD.RegistrationDate,LD.CreatedOn) AS decimal(10,2))/365 > 10 THEN 'MotorUnAssignedfinal_Exp'
                                    WHEN CD.FuleType='Petrol' AND CAST(DATEDIFF(DAY,CD.RegistrationDate,LD.CreatedOn) AS decimal(10,2))/365 > 14.5 AND (CD.RegistrationNo LIKE 'DL%' OR LEFT(CD.RegistrationNo,4) IN ('UP14','UP16','UP18','HR26','HR38','HR51','HR55','HR72','HR98')) THEN 'MotorUnAssignedfinal_Exp'
                                    WHEN CD.FuleType='Diesel' AND CAST(DATEDIFF(DAY,CD.RegistrationDate,LD.CreatedOn) AS decimal(10,2))/365 > 9.5  AND (CD.RegistrationNo LIKE 'DL%' OR LEFT(CD.RegistrationNo,4) IN ('UP14','UP16','UP18','HR26','HR38','HR51','HR55','HR72','HR98')) THEN 'MotorUnAssignedfinal_Exp'      
                                    WHEN LD.Utm_source='MOTOR_BOOKED' THEN 'FOS OFFICES' -- need to check by jitendra     
                                    WHEN CD.PolicyType='NEW' AND LD.Utm_source IN ('growthcartrade', 'growthcarinfo', 'growth91wheels') AND ISNULL(KFA.InsurerID,0)=0 AND LD.CreatedON > DATEADD(MINUTE,-10,GETDATE())  THEN 'KeepUnASSIGN' -- KEEP UnASSIGN till 10 mins												                                    
                                    WHEN CD.PolicyType='NEW' AND ISNULL(KFA.InsurerID,0)=0 AND LD.Utm_source IN ('growthcartrade', 'growthcarinfo', 'growth91wheels') THEN 'KeepUnASSIGN'                                     
                                    WHEN CD.PolicyType='NEW' AND CD.MakeId=33 THEN 'KeepUnASSIGN'
                                    WHEN CD.PolicyType='NEW' AND DATEDIFF(DAY,LD.CreatedON,CD.ExpectedDeliveryDate) <= 20 AND CD.MakeId=33 THEN 'BrandNew Experiment'                                     
                                    WHEN CD.PolicyType='NEW' THEN NULL    	
                                    WHEN (LD.Utm_source LIKE '%VehicleInfo%') AND ISNULL(KFA.InsurerID,0)=0 and CAST(CD.PreviousPolicyExpiryDate AS DATE)  BETWEEN CAST(GETDATE() AS DATE) AND CAST(GETDATE() + 7 AS DATE) then 'Car-Growth'	                                    
                                    WHEN (LD.Utm_source LIKE '%CarInfo%') AND ISNULL(KFA.InsurerID,0)=0 and CAST(CD.PreviousPolicyExpiryDate AS DATE)  BETWEEN CAST(GETDATE() AS DATE) AND CAST(GETDATE() + 14 AS DATE) then 'Car-Growth'	                                    
                                    WHEN LD.Utm_source IN ('growthcarinfo','growthvehicleinfo','vehicleinfo') AND ISNULL(KFA.InsurerID,0)=0 AND LD.CreatedON > DATEADD(MINUTE,-10,GETDATE())  THEN 'Motor_Retainers_UnASSIGN' -- KEEP UnASSIGN till 10 mins	 -- pinki need to confirm											                                                                                                                                     
                                    WHEN LD.Utm_source LIKE '%CRM%' AND LD.Utm_campaign LIKE '%external%' AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN '15DaysAfterExpiry-UNASSIGN'	
                                    WHEN LD.StateID IN ('30','26') AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN 'cartamilob'
                                    WHEN LD.StateID IN ('30','26') THEN 'Tamilan Team'
                                    WHEN LD.StateID IN ('17') AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN 'carmalayalam'
                                    --WHEN LD.StateID IN ('17') AND CAST(GETDATE() AS DATE) > CAST(CD.PreviousPolicyExpiryDate AS DATE) THEN 'Malayalam Offline'
                                    WHEN LD.StateID IN ('17') THEN 'Malayalam'
                                    WHEN LD.StateID IN ('16') AND CAST(GETDATE() AS DATE) > CAST(CD.PreviousPolicyExpiryDate AS DATE) AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN 'carkanadaoffline'
                                    WHEN LD.StateID IN ('16') AND CAST(GETDATE() AS DATE) > CAST(CD.PreviousPolicyExpiryDate AS DATE) THEN 'Kannada Offline' 
                                    WHEN LD.StateID IN ('16') AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE))  THEN 'carkannada' 
                                    WHEN LD.StateID IN ('16') AND CAST(GETDATE() AS DATE) > CAST(CD.PreviousPolicyExpiryDate AS DATE) THEN 'Kannada Offline' 
                                    WHEN LD.StateID IN ('16') THEN 'Kannada Offline' 
                                    WHEN LD.StateID IN (1,37) AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN 'carapts'                                    
                                    WHEN LD.StateID IN (1,37) THEN 'AP & TS'	
                                    --WHEN LD.StateID=11 AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN 'cargujfiveseven'
                                    --WHEN LD.StateID=11 AND ISNULL(LD.CityID,0) IN ('103') AND @LeadId%2=0  THEN 'Gujarat five-seven'                                                                            
                                    --WHEN LD.StateID=11 AND ISNULL(LD.CityID,0) NOT IN ('103') THEN 'Gujarat five-seven'                                                                            
                                    WHEN LD.StateID=20 AND CD.RegistrationDate IS NOT NULL AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN 'carmhfiveseven'   
                                    WHEN LD.StateID=20 AND CD.RegistrationDate IS NOT NULL THEN 'MH five-seven'
                                    WHEN (CAST(DATEDIFF(DAY,CD.RegistrationDate,LD.CreatedOn) AS decimal(10,2))/365)*12 BETWEEN 0 AND 32.5 AND CD.PreviousPolicyExpiryDate > CAST(GETDATE() + 14 AS DATE) THEN 'SAOD-UNASSIGN'   
                                    WHEN CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN '15DaysAfterExpiryPD'	                                                                                                                                                                                                                                                                                           	                                                                                                                                                                                                                      
                                    WHEN (CAST(DATEDIFF(DAY,CD.RegistrationDate,LD.CreatedOn) AS decimal(10,2))/365)*12 BETWEEN 0 AND 32.5 AND CD.PreviousPolicyExpiryDate BETWEEN CAST(GETDATE() AS DATE) AND CAST(GETDATE() + 14 AS DATE) THEN 'SAOD'                                                                        																											                                                                                                           
                                    --WHEN CityID IN (554,556) AND CD.PreviousPolicyExpiryDate IS NOT NULL AND CAST(CD.PreviousPolicyExpiryDate AS DATE) <> CAST(LD.CreatedON AS DATE) THEN 'Motor FOS GZB/FBD' 
									ELSE null END AS  Groupcode ,0 as LeadTypeId
							  ,CD.RegistrationDate,LD.CreatedON,LD.CustomerID,CD.RegistrationNo,LD.Utm_source,CD.PreviousPolicyExpiryDate
							  ,1 as IsAssist,CASE WHEN LAD.AssignToGroupId IN (466,716,1366,1419) THEN 'REASSIGN' ELSE LD.UTM_Medium END,CD.PolicySubType
                              ,CASE WHEN LAD.AssignToGroupId IN (1419) THEN LAD.AssignToGroupId ELSE NULL END AS GroupID
							  ,CD.MakeId,CD.TentativePurchaseDate,LD.StateID,CD.VehicleVariant
			  FROM            [Matrix].[CRM].[leaddetails150] LD  WITH(NOLOCK)              			  
			  LEFT  JOIN	  CRM.CarDetails CD (NOLOCK) on CD.LeadId=LD.LeadID
			  LEFT  JOIN	  [Matrix].[CRM].[LeadAssignDetails] LAD WITH (NOLOCK) ON LD.LeadId=LAD.LeadID AND LAD.IsLastAssigned=1  
			  LEFT  JOIN	  MTX.KeyFactors_Allocation KFA (NOLOCK) ON LD.LeadId=KFA.LeadID         			  	
			  LEFT  JOIN	  MTX.M_Variant MV (NOLOCK) ON CD.VehicleVariant=MV.VariantId     
			  LEFT  JOIN	  CRM.InvalidMobileNumbers IMN WITH(NOLOCK) ON IMN.MobileNo=LD.MobileNo 			  
			  WHERE           IMN.id  IS NULL   
              AND             LD.LeadID= @LeadId 
			  AND			  LD.ProductID = 117       
              AND             (LD.IsActive=1 OR LD.IsActive  IS NULL) 
              AND             LD.ParentID IS NULL
              AND             LD.MobileNo NOT IN ('8800950352','9849640779','9599903188','9912611104','9716262116')			  
			  AND			  LD.StatusID in (1,2,3,4,11)			  
              AND             LD.CustomerID NOT IN (35061581,19054071,19054048,19054004,35070194,19053976,35063061,35062746)			           
			  ORDER BY		  LD.LeadID DESC      
		  END
      ELSE 
		  BEGIN	
            DECLARE @Leads AS TABLE
            (
                LeadID BIGINT
            )  
            IF DATEPART(HOUR,GETDATE()) BETWEEN 15 AND 18 
                BEGIN
                    INSERT INTO @Leads
                    SELECT LeadID FROM [Matrix].[CRM].[leaddetails150] LD (NOLOCK)
                    WHERE ProductID=117 AND ParentID IS NULL AND LeadRank=255
                    AND LD.CreatedON BETWEEN CAST(GETDATE()-5 AS DATE) AND CAST(GETDATE()-3 AS DATE)  
                END     

			  SELECT DISTINCT TOP 10000 LD.LeadID,LD.ProductID
							  ,CASE WHEN ld.LeadSource ='MyAcc' THEN 2 
										 WHEN ld.Utm_source='Health RM' THEN 1																			
									ELSE (SELECT [MTX].[GetLeadRank_Car_Allocation](ISNULL(KFA.InsurerID,0),ISNULL(KFA.PreviousBooking,0),CD.RegistrationNo,CD.RegistrationDate,CD.PreviousPolicyExpiryDate,LD.LeadSource,LD.CreatedOn,CD.PolicySubType
                                    ,CASE WHEN CD.PolicyType='NEW' THEN 1 ELSE 0 END,LD.cityID,CD.MakeID,LD.Utm_Source,CD.RegisteredStateId,CD.CarRegisterCityId,CD.ExpectedDeliveryDate)) END LeadRank 
							  ,LD.Utm_campaign,LD.LeadSource,LD.ChatStatus,LD.LeadScore AS LeadEntryPPL
							  ,ISNULL(LD.LeadRank,0) LeadGrade,LD.StatusID,ISNULL(LD.SubStatusID,0) SubStatusID
							  ,CASE --WHEN LD.leadID%30=0 THEN 'Call Centre Unassigned' 
                                    WHEN ISNULL(CD.PolicyType,'')<>'NEW' AND len(CD.RegistrationNo) <=5 then 'KEEP UNASSIGN'
                                    WHEN DATEDIFF(DAY,CAST(LD.CreatedON AS DATE),CD.PreviousPolicyExpiryDate) = 10 AND ISNULL(KFA.InsurerID,0)=0 then 'KEEP UNASSIGN'
                                    WHEN LD.LeadSource='CrossSell' AND LD.Utm_source='MotorOnCorp' THEN 'Motoroncorp'
                                    WHEN CD.RegistrationNo='23BH2342k' THEN 'UNASSIGN'                                   
                                    WHEN CD.PreviousPolicyExpiryDate IS NOT NULL AND DATEDIFF(DAY,CAST(GETDATE() AS DATE),CD.PreviousPolicyExpiryDate) > 45 THEN '45 days expiry'
                                    WHEN LD.ChatStatus <> 0 AND LD.Utm_source NOT IN ('CRMSMS','CRMPmailer')  THEN 'CallOnChat-Motor'                                                                                                                                                                                  
                                    WHEN LD.LeadSource='Crosssell' AND LD.Utm_source='Health_RM' THEN 'Inbound (Car)'
                                    WHEN LD.LeadSource='whatsapp' AND LD.Utm_source='whatsapp_crm_sales' THEN 'Inbound (Car)'
                                    WHEN CD.IsPreviousPolicyDateAssumed=1 OR CD.IsQuotesSeen=0 THEN 'MotorUnAssignedfinal_Exp'
                                    WHEN LD.Utm_source LIKE '%crm%' AND LD.Utm_campaign LIKE '%external%' AND CAST(DATEDIFF(DAY,CD.RegistrationDate,LD.CreatedOn) AS decimal(10,2))/365 > 10 THEN 'MotorUnAssignedfinal_Exp'
                                    WHEN CD.FuleType='Petrol' AND CAST(DATEDIFF(DAY,CD.RegistrationDate,LD.CreatedOn) AS decimal(10,2))/365 > 14.5 AND (CD.RegistrationNo LIKE 'DL%' OR LEFT(CD.RegistrationNo,4) IN ('UP14','UP16','UP18','HR26','HR38','HR51','HR55','HR72','HR98')) THEN 'MotorUnAssignedfinal_Exp'
                                    WHEN CD.FuleType='Diesel' AND CAST(DATEDIFF(DAY,CD.RegistrationDate,LD.CreatedOn) AS decimal(10,2))/365 > 9.5  AND (CD.RegistrationNo LIKE 'DL%' OR LEFT(CD.RegistrationNo,4) IN ('UP14','UP16','UP18','HR26','HR38','HR51','HR55','HR72','HR98')) THEN 'MotorUnAssignedfinal_Exp'      
                                    WHEN LD.Utm_source='MOTOR_BOOKED' THEN 'FOS OFFICES' -- need to check by jitendra     
                                    WHEN CD.PolicyType='NEW' AND LD.Utm_source IN ('growthcartrade', 'growthcarinfo', 'cardekho','vehicleinfo') AND ISNULL(CD.MakeId,0) <> 33 THEN 'BrandNew Experiment'
                                    WHEN CD.PolicyType='NEW' AND LD.Utm_source IN ('growthcartrade', 'growthcarinfo', 'growth91wheels') AND ISNULL(KFA.InsurerID,0)=0 AND LD.CreatedON > DATEADD(MINUTE,-10,GETDATE())  THEN 'KeepUnASSIGN' -- KEEP UnASSIGN till 10 mins												                                    
                                    WHEN CD.PolicyType='NEW' AND ISNULL(KFA.InsurerID,0)=0 AND LD.Utm_source IN ('growthcartrade', 'growthcarinfo', 'growth91wheels') THEN 'New Car'                                     
                                    WHEN CD.PolicyType='NEW' AND CD.MakeId=33 THEN 'New Car'
                                    WHEN CD.PolicyType='NEW' AND DATEDIFF(DAY,LD.CreatedON,CD.ExpectedDeliveryDate) <= 20 AND CD.MakeId=33 THEN 'BrandNew Experiment'                                     
                                    WHEN CD.PolicyType='NEW' AND LD.StateID IN ('1','37') THEN 'Telugu BrandNew'
                                    WHEN CD.PolicyType='NEW' AND LD.StateID IN ('30','26') THEN 'Tamil BrandNew'   
                                    WHEN CD.PolicyType='NEW' THEN NULL   
                                    WHEN ISNULL(LD.StateID,0) NOT IN (1,11,16,17,20,30,37) AND (LD.Utm_source LIKE '%CarInfo%' OR (LD.Utm_source LIKE '%CRM%' AND LD.Utm_campaign LIKE '%external%')) AND ISNULL(KFA.InsurerID,0)=0 and CAST(CD.PreviousPolicyExpiryDate AS DATE)  BETWEEN CAST(GETDATE() AS DATE) AND CAST(GETDATE() + 14 AS DATE) then 'Car-Growth' 
                                    WHEN ISNULL(LD.StateID,0) NOT IN (1,11,16,17,20,30,37) AND (LD.Utm_source LIKE '%VehicleInfo%' OR (LD.Utm_source LIKE '%CRM%' AND LD.Utm_campaign LIKE '%external%')) AND ISNULL(KFA.InsurerID,0)=0 and CAST(CD.PreviousPolicyExpiryDate AS DATE)  BETWEEN CAST(GETDATE() AS DATE) AND CAST(GETDATE() + 7 AS DATE) then 'Car-Growth' 	
                                    WHEN (LD.Utm_source LIKE '%CarInfo%' OR LD.Utm_source LIKE '%VehicleInfo%') AND ISNULL(KFA.InsurerID,0)=0 THEN 'KEEP UNASSIGN'	                                    
                                    WHEN LD.Utm_source IN ('growthcarinfo','growthvehicleinfo','vehicleinfo') AND ISNULL(KFA.InsurerID,0)=0 AND LD.CreatedON > DATEADD(MINUTE,-10,GETDATE())  THEN 'Motor_Retainers_UnASSIGN' -- KEEP UnASSIGN till 10 mins	 -- pinki need to confirm											                                                                                                                                     
                                    WHEN LD.Utm_source LIKE '%CRM%' AND LD.Utm_campaign LIKE '%external%' AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN '15DaysAfterExpiry-UNASSIGN'	
                                    WHEN LD.StateID IN ('30','26') AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN 'cartamilob'
                                    WHEN LD.StateID IN ('30','26') THEN 'Tamilan Team'
                                    WHEN LD.StateID IN ('17') AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN 'carmalayalam'
                                    --WHEN LD.StateID IN ('17') AND CAST(GETDATE() AS DATE) > CAST(CD.PreviousPolicyExpiryDate AS DATE) THEN 'Malayalam Offline'
                                    WHEN LD.StateID IN ('17') THEN 'Malayalam'
                                    WHEN LD.StateID IN ('16') AND CAST(GETDATE() AS DATE) > CAST(CD.PreviousPolicyExpiryDate AS DATE) AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN 'carkanadaoffline'                                    
                                    WHEN LD.StateID IN ('16') AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE))  THEN 'carkannada'                                     
                                    WHEN LD.StateID IN ('16') THEN 'Kannada Offline' 
                                    WHEN LD.StateID IN (1,37) AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN 'carapts'                                    
                                    WHEN LD.StateID IN (1,37) THEN 'AP & TS'	
                                    --WHEN LD.StateID=11 AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN 'cargujfiveseven'
                                    --WHEN LD.StateID=11  THEN 'Gujarat five-seven'                                                                            
                                    WHEN LD.StateID=20 AND CD.RegistrationDate IS NOT NULL AND CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN 'carmhfiveseven'   
                                    WHEN LD.StateID=20 AND CD.RegistrationDate IS NOT NULL THEN 'MH five-seven'
                                    WHEN (CAST(DATEDIFF(DAY,CD.RegistrationDate,LD.CreatedOn) AS decimal(10,2))/365)*12 BETWEEN 0 AND 32.5 AND CD.PreviousPolicyExpiryDate > CAST(GETDATE() + 14 AS DATE) THEN 'KEEP UNASSIGN'   
                                    WHEN CAST(CD.PreviousPolicyExpiryDate AS DATE) >= DATEADD(DAY,15,CAST(LD.CreatedON AS DATE)) THEN '15DaysAfterExpiryPD'	                                                                                                                                                                                                                                                                                           	                                                                                                                                                                                                                      
                                    WHEN (CAST(DATEDIFF(DAY,CD.RegistrationDate,LD.CreatedOn) AS decimal(10,2))/365)*12 BETWEEN 0 AND 32.5 AND CD.PreviousPolicyExpiryDate BETWEEN CAST(GETDATE() AS DATE) AND CAST(GETDATE() + 14 AS DATE) THEN 'SAOD'                                    
                                    --WHEN CityID IN (554,556) AND CD.PreviousPolicyExpiryDate IS NOT NULL AND CAST(CD.PreviousPolicyExpiryDate AS DATE) <> CAST(LD.CreatedON AS DATE) THEN 'Motor FOS GZB/FBD' 
									ELSE null END AS  Groupcode ,0 as LeadTypeId
							  ,CD.RegistrationDate,LD.CreatedON,LD.CustomerID,CD.RegistrationNo,LD.Utm_source,CD.PreviousPolicyExpiryDate
							  ,1 as IsAssist,CASE WHEN LAD.AssignToGroupId IN (466,716,1366,1419) THEN 'REASSIGN' ELSE LD.UTM_Medium END,CD.PolicySubType
                              ,CASE WHEN LAD.AssignToGroupId IN (1419) THEN LAD.AssignToGroupId ELSE NULL END AS GroupID
							  ,CD.MakeId,CD.TentativePurchaseDate,LD.StateID,CD.VehicleVariant
			  FROM            [Matrix].[CRM].[leaddetails150] LD  WITH(NOLOCK)              			  
			  LEFT  JOIN	  CRM.CarDetails CD (NOLOCK) on CD.LeadId=LD.LeadID
			  LEFT  JOIN	  [Matrix].[CRM].[LeadAssignDetails] LAD WITH (NOLOCK) ON LD.LeadId=LAD.LeadID AND LAD.IsLastAssigned=1  
			  LEFT  JOIN	  MTX.KeyFactors_Allocation KFA (NOLOCK) ON LD.LeadId=KFA.LeadID         			  	
			  LEFT  JOIN	  MTX.M_Variant MV (NOLOCK) ON CD.VehicleVariant=MV.VariantId     
			  LEFT  JOIN	  CRM.InvalidMobileNumbers IMN WITH(NOLOCK) ON IMN.MobileNo=LD.MobileNo 			  
			  WHERE           IMN.id  IS NULL   
              AND             (   
                                LD.CreatedON BETWEEN @FromDate AND @ToDate 
                                OR LD.LeadID IN (SELECT LeadID FROM @Leads)                                
                              )       			  
			  AND			  LD.ProductID = 117       
              AND             (LD.IsActive=1 OR LD.IsActive  IS NULL) 
              AND             LD.ParentID IS NULL
              AND             LD.MobileNo NOT IN ('8800950352','9849640779','9599903188','9912611104','9716262116')
			  AND			  ISNULL(LD.LeadSource,0) NOT IN('NRI','Inbound','Referral','Renewal','SMS','Cross Sell','Reopen','ACAFF','ACAFFAPP','Reopen')								
			  AND			  ISNULL(LD.Utm_source,0) NOT IN ('OfflineAffiliate','CarRenewalReminder','RPD','swarafincare','fleet','monthlypersistencyleads')
              AND             ISNULL(LD.Utm_campaign,'') NOT IN ('Policy_Bazaar_Tier_100Brand')
			  AND			  LD.StatusID in (1,2,3,4,11)
			  AND			  (                                     
									LAD.ID IS NULL OR 
									(LAD.AssignToGroupId IN (1419)	AND LAD.AssignedToUserID is null) 
									--OR (LAD.AssignToGroupId =466 AND LAD.AssignedToUserID is null AND LAD.CreatedOn BETWEEN CAST(GETDATE() AS DATE) AND DATEADD(MINUTE,-7,GETDATE()))
							  )
			  AND			  (LD.ProductType IS NULL  OR LD.ProductType=1) and ISNULL(LD.Country,'') NOT IN( 'OTHERS','999')			  
			  AND			  ISNULL(LD.leadrank,0) <> 200 	
			  AND			  1= CASE WHEN (LD.Utm_source='zf' AND LD.UTM_Medium='unassign') OR (LD.Utm_source='organic' AND LD.UTM_Medium IN ('rto-city','rto-state','articles','vehicle-detail','road-tax','rc-status','hsrp','driving-licence','challan')) OR LD.Name = 'Random Something' THEN 0 ELSE 1 END
              AND             LD.CustomerID NOT IN (35061581,19054071,19054048,19054004,35070194,19053976,35063061,35062746)
			  --AND			  LD.ChatStatus <> 1	              
			  ORDER BY		  LD.LeadID DESC      
		  END
	  
END

