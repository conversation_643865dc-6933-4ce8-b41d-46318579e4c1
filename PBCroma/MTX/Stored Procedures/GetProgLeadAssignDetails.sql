

--[MTX].[GetProgLeadAssignDetails] 762963243,1,0

CREATE PROCEDURE [MTX].[GetProgLeadAssignDetails]  --143790303,1,0
	(
		@LeadId BIGINT=0,
		@CTC BIT=0,
		@IB BIT=0
	)
AS
BEGIN
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
	SET NOCOUNT ON;
	
	DECLARE @empdetails AS TABLE
	(
		EmployeeId VARCHAR(50),
		AssignedToUserID BIGINT,
		ProductID TINYINT,
		CustomerID BIGINT,
		Name VARCHAR (50),
		IsOneLead BIT
	)
	
	DECLARE @CreatedOn datetime = CAST(GETDATE() AS DATE)
	DECLARE @ParentLead BIGINT
	DECLARE @ProductID SMALLINT
	DECLARE @CustomerID BIGINT
	DECLARE @Name VARCHAR(100)
	DECLARE @LeadSource VARCHAR(100)
	DECLARE @Utm_Source VARCHAR(255)
	DECLARE @LeadSource_lead VARCHAR(30)
	DECLARE @StatusID SMALLINT
	DECLARE @LeadRank SMALLINT=0
	DECLARE @Country VARCHAR(50)
    DECLARE @StateID TINYINT
    DECLARE @CityID INT
    DECLARE @CustomUtm VARCHAR(255)

    IF (SELECT COUNT(1) FROM [MTX].[CTC_OB_Tracking] WHERE LeadID=@LeadId AND ts > DATEADD(MINUTE,-60,GETDATE())) >= 2
        BEGIN
            SELECT * FROM @empdetails WHERE 1=2
			RETURN;	
        END

	SELECT		@ParentLead=ParentID,@ProductID=ProductID,@CustomerID=CustomerID,@Name=Name,@StatusID=StatusID
                ,@LeadSource_lead=LeadSource,@Country=Country,@Utm_Source=Utm_source,@StateID=StateID,@CityID=LD.CityID
                ,@CustomUtm=[MTX].[GetCustomUTM](LD.UTM_Source,LD.UTM_Medium,LD.LeadSource,LD.UTM_Term,LD.Utm_campaign,'',LD.ProductID) 
	FROM		[Matrix].[CRM].[Leaddetails] LD (NOLOCK)
	INNER JOIN  CRM.LeadStatus LS (NOLOCK) ON LD.LeadID=LS.LeadID AND LS.IsLastStatus=1
	LEFT JOIN	CRM.InvalidMobileNumbers INV ON LD.MobileNo=INV.MobileNo
	WHERE		LD.LeadID=@LeadId AND INV.id IS NULL
	AND			ISNULL(Source,'') <> 'BotLead'
	IF (@@ROWCOUNT < 1)
		BEGIN
			SELECT * FROM @empdetails WHERE 1=2
			RETURN;	
		END
	
	SET @LeadId=ISNULL(@ParentLead,@LeadId)

	IF (@ProductID=2 AND @LeadSource_lead <> 'RENEWAL')
		BEGIN
			IF EXISTS(SELECT 1 FROM [Matrix].[CRM].[LeadAssignDetails] LAD (NOLOCK) INNER JOIN CRM.UserGroupMaster UGM (NOLOCK) ON LAD.AssignToGroupId=UGM.UserGroupID WHERE LeadID=@LeadId AND LAD.IsLastAssigned=1 AND UGM.ProcessID=4)
				BEGIN
					SET @LeadSource_lead='RENEWAL'
				END
		END
	ELSE IF (@ProductID=115)
		BEGIN
			IF ISNULL(@Country,'') NOT in ('392','91','999','INDIA','0','NULL','')  
					AND (@Utm_Source LIKE '%retainer%' or @Utm_Source LIKE '%appoint%' or @Utm_Source LIKE '%Paisa%') 
					AND ISNULL(@Utm_Source,'') <> 'Paisa_Bureau_Annuity'
				SET @LeadRank=11			
		END

	/*not needed */
	--SELECT TOP 1 @LeadSource=LeadSource FROM CTC.Schedular WHERE LeadID=@LeadId ORDER BY id DESC
	
	
	
	IF @IB=1 AND @CTC=1
		BEGIN
			INSERT INTO @empdetails(EmployeeId,AssignedToUserID,ProductID,CustomerID,Name,IsOneLead)
			SELECT TOP 1 UD.EmployeeId, LAD.AssignedToUserID, LDD.ProductID,LDD.CustomerID,LDD.Name,1
			FROM CRM.UserDetails UD WITH(NOLOCK)
			INNER JOIN [Matrix].[CRM].[LeadAssignDetails] LAD WITH(NOLOCK) ON UD.UserID = LAD.AssignedToUserID AND IsLastAssigned=1 AND LeadID=@LeadId AND UD.IsProgressive=1 AND LAD.AssignedToUserID > 0
			INNER JOIN [Matrix].[CRM].[Leaddetails] LDD WITH(NOLOCK) ON LDD.LeadID = LAD.LeadID	AND LDD.ProductID=117			
			INNER JOIN CRM.LoginDetails LD WITH(NOLOCK) ON LD.UserID = UD.UserID AND LD.CreatedOn > @CreatedOn AND LD.Active=1 AND LD.LogInURL LIKE '%matrixliveapi%'
			
			IF @@ROWCOUNT > 0			
				INSERT INTO [MTX].[CTC_OB_Tracking] (LeadID,ts,UserID,LeadType) VALUES(@LeadId,GETDATE(),(SELECT AssignedToUserID FROM @empdetails),'IB')
								
		END		
	
	ELSE IF @CTC=1 AND @LeadSource_lead <> 'RENEWAL'
		BEGIN
			DECLARE @flag TINYINT=1
			DECLARE @IsCallBack TINYINT=0
			--IF EXISTS(SELECT 1 FROM [MTX].[CTC_OB_Tracking] (NOLOCK) WHERE LeadID= @LeadId AND ts > DATEADD(MINUTE,-45,GETDATE()))
				--SET @flag=0
			
			IF @ProductID=101
				SELECT @IsCallBack=COUNT(1) FROM CRM.EventDetails (NOLOCK) WHERE LeadID=@LeadId AND eventdate > getdate()

			INSERT INTO @empdetails(EmployeeId,AssignedToUserID,ProductID,CustomerID,Name,IsOneLead)
			SELECT TOP 1 UD.EmployeeId, LAD.AssignedToUserID, LDD.ProductID,LDD.CustomerID,LDD.Name,CASE WHEN ISNULL(UGM.ProcessID,0) = 4 THEN 0 ELSE UGM.IsOneLead END
			FROM	   CRM.UserDetails UD WITH(NOLOCK)
			INNER JOIN CRM.UserGroupRoleMapNew UGRN (NOLOCK) ON UD.UserID=UGRN.UserId AND UGRN.RoleSuperId=11
			INNER JOIN CRM.UserGroupMaster UGM (NOLOCK) ON UGRN.GroupId=UGM.UserGroupID
			INNER JOIN [Matrix].[CRM].[LeadAssignDetails] LAD WITH(NOLOCK) ON UD.UserID = LAD.AssignedToUserID AND IsLastAssigned=1 AND LeadID=@LeadId AND LAD.AssignedToUserID > 0
			INNER JOIN [Matrix].[CRM].[leaddetails150] LDD WITH(NOLOCK) ON LDD.LeadID = LAD.LeadID	
			AND		   ((LDD.ProductID IN (117,3) AND LDD.StatusID IN (1,2,3,4,11)) OR (LDD.ProductID =2) 
			           OR (LDD.ProductID =115 AND @LeadRank=11) 
                       OR (LDD.ProductID =7 AND @LeadRank IN (477, 478, 571, 572, 371, 41,42,43,44)) 
					   OR (@LeadSource LIKE '%myacc%')
					   OR (LDD.ProductID IN (131) AND LDD.StatusID IN (3,4,11))	
					   OR (LDD.ProductID IN (101) AND LDD.StatusID IN (4,11) AND @IsCallBack > 0))			
			INNER JOIN CRM.LoginDetails LD WITH(NOLOCK) ON LD.UserID = UD.UserID AND LD.CreatedOn > @CreatedOn AND LD.Active=1 			
			--AND		   @flag=CASE WHEN @ProductID IN (2) THEN @flag ELSE 1 END   
			--WHERE		1= CASE WHEN @LeadId=762963243 THEN 0 ELSE 1 END
			
			IF @@ROWCOUNT > 0			
				INSERT INTO [MTX].[CTC_OB_Tracking] (LeadID,ts,UserID,LeadType, LeadSource) VALUES(@LeadId,GETDATE(),(SELECT AssignedToUserID FROM @empdetails),'CTC', @LeadSource)
			ELSE IF @ProductID=2 AND @StatusID IN (1,2,3,4,11) 
				BEGIN 	
					INSERT INTO @empdetails(EmployeeId,AssignedToUserID,ProductID,CustomerID,Name,IsOneLead)				
					EXEC [MTX].[CTC_OB_Assignment] @LeadId,@ProductID,@CustomerID,@Name,@Country,@Utm_Source,@StateID,@CityID,@CustomUtm,@LeadSource_lead
					IF @@ROWCOUNT > 0
						INSERT INTO [MTX].[CTC_OB_Tracking] (LeadID,ts,UserID,LeadType, LeadSource) VALUES(@LeadId,GETDATE(),(SELECT AssignedToUserID FROM @empdetails),'CTC_OB', @LeadSource)
				END
			ELSE IF @ProductID=117 AND @StatusID IN (1,2,3,4,11) 
				BEGIN 	
                    DECLARE @UserID BIGINT=0,@EmpID VARCHAR(50)=null
					
					EXEC [MTX].[CTC_OB_Assignment_Car] @LeadId,@ProductID,@CustomerID,@Name,@LeadRank,@EmpID OUT,@UserID OUT
                    
                    IF(@UserID > 0 AND ISNULL(@EmpID,'') <> '')
                        BEGIN
                            INSERT INTO @empdetails(EmployeeId,AssignedToUserID,ProductID,CustomerID,Name,IsOneLead)
                            SELECT @EmpID,@UserID,@ProductID,@CustomerID,@Name,1				                            
                            INSERT INTO [MTX].[CTC_OB_Tracking] (LeadID,ts,UserID,LeadType, LeadSource) VALUES(@LeadId,GETDATE(),(SELECT AssignedToUserID FROM @empdetails),'CTC_OB', (SELECT EmployeeId FROM @empdetails))
                        END    
				END
			ELSE IF @ProductID=115 AND @StatusID IN (1,2,3,4,11) AND @LeadRank=11
				BEGIN 	
					INSERT INTO @empdetails(EmployeeId,AssignedToUserID,ProductID,CustomerID,Name,IsOneLead)				
					EXEC [MTX].[CTC_OB_Assignment_INV] @LeadId,@ProductID,@CustomerID,@Name,@LeadRank
					IF @@ROWCOUNT > 0
						INSERT INTO [MTX].[CTC_OB_Tracking] (LeadID,ts,UserID,LeadType, LeadSource) VALUES(@LeadId,GETDATE(),(SELECT AssignedToUserID FROM @empdetails),'CTC_OB', @LeadSource)
				END						
		END
	ELSE IF @IB=1
		BEGIN
			INSERT INTO @empdetails(EmployeeId,AssignedToUserID,ProductID,CustomerID,Name,IsOneLead)
			SELECT TOP 1 UD.EmployeeId, LAD.AssignedToUserID, LDD.ProductID,LDD.CustomerID,LDD.Name,1
			FROM CRM.UserDetails UD WITH(NOLOCK)
			INNER JOIN [Matrix].[CRM].[LeadAssignDetails] LAD WITH(NOLOCK) ON UD.UserID = LAD.AssignedToUserID AND IsLastAssigned=1 AND LeadID=@LeadId AND UD.IsProgressive=1 AND LAD.AssignedToUserID > 0
			INNER JOIN [Matrix].[CRM].[Leaddetails] LDD WITH(NOLOCK) ON LDD.LeadID = LAD.LeadID	AND LDD.ProductID=117			
			INNER JOIN CRM.LoginDetails LD WITH(NOLOCK) ON LD.UserID = UD.UserID AND LD.CreatedOn > @CreatedOn AND LD.Active=1 AND LD.LogInURL LIKE '%matrixliveapi%'
			WHERE	   DATEPART(HOUR,GETDATE()) BETWEEN 10 AND 18  	
			
			IF @@ROWCOUNT > 0			
				INSERT INTO [MTX].[CTC_OB_Tracking] (LeadID,ts,UserID,LeadType) VALUES(@LeadId,GETDATE(),(SELECT AssignedToUserID FROM @empdetails),'IB_SEND')
		END		
	ELSE
		BEGIN
            
            INSERT INTO @empdetails(EmployeeId,AssignedToUserID,ProductID,CustomerID,Name,IsOneLead)
            SELECT TOP 1 UD.EmployeeId, LAD.AssignedToUserID, LDD.ProductID,LDD.CustomerID,LDD.Name,1
            FROM		CRM.UserDetails UD WITH(NOLOCK)
            INNER JOIN	[Matrix].[CRM].[LeadAssignDetails] LAD WITH(NOLOCK) ON UD.UserID = LAD.AssignedToUserID AND IsLastAssigned=1 AND LeadID=@LeadId AND LAD.AssignedToUserID > 0
            INNER JOIN	[Matrix].[CRM].[Leaddetails] LDD WITH(NOLOCK) ON LDD.LeadID = LAD.LeadID	
            INNER JOIN	CRM.LoginDetails LD WITH(NOLOCK) ON LD.UserID = UD.UserID AND LD.CreatedOn > @CreatedOn AND LD.Active=1
            WHERE		(
                                (LDD.ProductID =117 AND LAD.AssignToGroupId NOT IN (466,906)) 
                            OR  (LDD.ProductID IN (SELECT ID FROM dbo.Products (NOLOCK) WHERE ProductName LIKE '%international%'))
                            OR  (LDD.ProductID =2 AND LAD.AssignToGroupId IN (1181,1287,1471))
                        )						
            IF @@ROWCOUNT > 0			
                INSERT INTO [MTX].[CTC_OB_Tracking] (LeadID,ts,UserID,LeadType) VALUES(@LeadId,GETDATE(),(SELECT AssignedToUserID FROM @empdetails),'Chat')
                    
		END
	
	SELECT * FROM @empdetails WHERE IsOneLead=1
	
	--8935,8393,7739,9693,10245
	
END