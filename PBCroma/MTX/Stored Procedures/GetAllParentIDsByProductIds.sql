﻿
-- =============================================
-- Author:		
-- Create date:        17-06-2024
-- Description:	Get Parent IDs
-- =============================================

CREATE OR ALTER PROCEDURE  [MTX].[GetAllParentIDsByProductIds]( 
	@FromDate datetime, 
	@ToDate datetime,
    @ProductIds NVARCHAR(100) = ''
) 
AS 
BEGIN
    SET DEADLOCK_PRIORITY LOW                       
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
    
    IF @ProductIds = ''
    BEGIN   
        RETURN;
    END

     -- Create a table variable to hold the product IDs
    DECLARE @ProductIdTable TABLE (ProductId INT);
    
    -- Split the comma-separated product IDs and insert into table variable
    INSERT INTO @ProductIdTable (ProductId)
    SELECT CAST(value AS INT)
    FROM STRING_SPLIT(@ProductIds, ',')
    WHERE RTRIM(value) <> '';

    

    SELECT distinct ISNULL(LD.ParentID,LD.LeadID) AS ParentID
    FROM matrix.crm.Leaddetails150 LD
	INNER JOIN  PBCroma.CRM.LeadStatus ls with(nolock) on ls.LeadID=LD.LeadID and ls.IsLastStatus=1 AND ls.StatusID IN (1,2,3,4,11)
    INNER JOIN @ProductIdTable P ON ld.ProductId = P.ProductId
    WHERE LD.CreatedON between @FromDate AND @ToDate
	--ORDER BY LD.LeadID DESC;

    END;