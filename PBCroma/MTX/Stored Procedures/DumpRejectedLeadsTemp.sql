

-- =============================================
-- Author:		<PERSON><PERSON><PERSON><PERSON>
-- Create date:        24-06-2024
-- Description:	Dump Rejected Leads Temp
-- =============================================

CREATE PROCEDURE  [MTX].[DumpRejectedLeadsTemp]( 
	@LeadID BIGINT,
	@ProductID SMALLINT,
	@CurrDate datetime,
	@Reason Varchar(100)
) 
AS 
BEGIN
    SET DEADLOCK_PRIORITY LOW                       
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED

	IF EXISTS (SELECT TOP 1 LeadID FROM [PBCroma].[MTX].[RejectedLeadsTempDump] WHERE LeadID = @LeadID)
	BEGIN
        
        UPDATE [PBCroma].[MTX].[RejectedLeadsTempDump]
        SET [count] = [count] + 1
        WHERE LeadID = @LeadID;
    END

	else
	BEGIN
    INSERT INTO [PBCroma].[MTX].[RejectedLeadsTempDump](LeadID,ProductID,ts,RejectionSource,[Count]) VALUES(@LeadID,@ProductID,GETDATE(),@Reason,1);
	End

    END;