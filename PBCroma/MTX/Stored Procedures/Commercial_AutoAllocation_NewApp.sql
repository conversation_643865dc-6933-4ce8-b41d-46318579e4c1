CREATE PROCEDURE [MTX].[Commercial_AutoAllocation_NewApp]
AS
BEGIN
--RETURN;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED                                  
	SET NOCOUNT ON                                
	SET DEADLOCK_PRIORITY LOW   

   --===========/*Declaration  Section*/===================== 
	DECLARE @FROMDATE DATETIME 
	DECLARE @TODATE DATETIME 
	DECLARE @ProductID TINYINT=117
	DECLARE @FromLoginDate DATETIME= CAST(CAST(Getdate() as DATE) as Varchar)+' 00:00:00.000'    
	DECLARE @ToLoginDate DATETIME= CAST(CAST(Getdate() as DATE) as Varchar)+' 23:59:59.998'
--=============================================================
--========================================================Set Date to Previous day 10:00hrs to 18:00hrs =============================================================
	----------- Condition for Executing Morning Batch ------------------------ 

	SET @FromDate =CAST(CAST(GETDATE() AS DATE) as Varchar)+' 07:00:00.000'    

	IF(DATEPART(HH, GETDATE()) >= 10 AND DATEPART(HH, GETDATE()) < 21) 
	BEGIN 		
		SET @FromDate =CAST(CAST(Getdate()-1 as DATE) as Varchar)+' 19:00:00.000'    		
	END 
	
	IF DATEPART(HH, GETDATE()) >= 21
		RETURN;
	
--====================================Obtaining All Term Allocable Leads(CAMPARE THE CONDITIONS WITH LIVE SCRIPT BEFORE DEPLOYMENT)======================			
    
	CREATE TABLE #LeadTable
	(
		  ID INT IDENTITY(1,1), LeadId BIGINT,MobileNo VARCHAR(50),LeadRank SMALLINT,Createdon DATETIME,CustId BIGINT,StatusId TINYINT,SubStatusId SMALLINT
		  ,IsAllocable BIT ,policyexpirydate datetime,GroupID SMALLINT,VehicleSubClass VARCHAR(100)	
          ,RegistrationNo VARCHAR(30),Utm_Source VARCHAR(255)	 
	)				
   
    INSERT INTO #LeadTable
	(
		LeadId,Createdon,CustId,StatusId,SubStatusId,IsAllocable,policyexpirydate,GroupID,VehicleSubClass,RegistrationNo,Utm_Source
	)	
    SELECT			TOP 3000 LD.LeadID,LD.CreatedON,LD.CustomerID,ISNULL(LD.StatusID,1),ISNULL(LD.SubStatusID,0)
                        ,CASE WHEN LD.Utm_source='whatsapp_crm_sales' THEN 0 ELSE 1 END AS IsAllocable,CD.PreviousPolicyExpiryDate
                        ,CASE 
                                WHEN LD.Utm_source IN ('RMEnquiry') THEN 3550    
                                WHEN CD.PreviousPolicyExpiryDate IS NOT NULL AND CD.PreviousPolicyExpiryDate > CAST(GETDATE()+45 AS DATE) THEN 0
                                WHEN LD.LeadSource='CrossSell' AND LD.Utm_source='CommercialOnCorp' THEN 3409                                   
                                WHEN CD.RegistrationNo LIKE 'TN%' THEN 3542
                                WHEN ISNULL(LD.Utm_source,'') LIKE '%carinfo%' OR ISNULL(LD.Utm_source,'') IN ('vehicleinfo','carinfo','car-info','vahanx') 
                                    OR LD.Utm_source LIKE '%transport%' OR LD.Utm_source LIKE '%paisa%' OR LD.UTM_Medium LIKE '%paisa%' OR LD.Utm_source LIKE '%lets%'
                                    OR LD.UTM_Medium IN ('rto-city','rto-state','road-tax','rc-status','driving-licence','challan','vehicle-detail','hsrp','bike-challan')    THEN 3543                                   
                                ELSE 3055 END AS GroupID
                        ,CD.VehicleSubClass,CD.RegistrationNo,LD.Utm_source	                        	                        
    FROM            [Matrix].[CRM].[leaddetails150] LD  WITH(NOLOCK)	
    LEFT   JOIN     CRM.CarDetails CD (NOLOCK) ON LD.LeadID=CD.LeadId
    LEFT   JOIN     [Matrix].[CRM].[LeadAssignDetails] LAD(NOLOCK) ON LAD.LeadID=LD.LeadID AND LAD.IsLastAssigned=1 
    LEFT   JOIN     CRM.InvalidMobileNumbers INV (NOLOCK) ON LD.CustomerID=INV.CustID		
    WHERE           LD.CreatedON BETWEEN @FromDate	AND GETDATE()
    AND				LD.ProductID = 139  AND  (LD.IsActive=1 OR LD.IsActive  IS NULL) AND  LD.ParentID IS NULL	
    AND				LD.statusID IN (1,2,3,4,11)
    AND				ISNULL(LD.Utm_source,'') NOT IN ('OfflineAffiliate','CV_Renewal','swarafincare','AutoLeadCreation')			        
    AND				LAD.ID IS NULL	
    AND				INV.id IS NULL  
    AND             ISNULL(LD.StateID,0) <> 25 
    AND             LD.CustomerID NOT IN (121966235,61698464,24981,285582,99850044,134607,107702374,424,34849,14660959,96653864,99761951,140,112423343,17556611,101303023,100381355,22939032,2497,21835920,112111305,117038488,80601553,35148309,1889,10706967,10790137,23083623,23082331,912349,382008,23023434,697056,10778581,2653031,901172,	23099733,10744755,23118018,10621790,23117877,10641392,10618592,10759064,23006199,1160828,10745442,10742311,3389,10607405,23027568,10776341,10655088,10794437,102,23075042,23082930,10748252,314355,23021371,23090417,10632599,23120588,23058150,23075093,349472,23066915,23123007)         
    ORDER BY        CASE WHEN LD.LeadSource='CrossSell' THEN 10000 WHEN CD.PreviousPolicyExpiryDate BETWEEN CAST(GETDATE() AS DATE) AND CAST(GETDATE()+10 AS DATE) THEN 9999 ELSE LD.LeadScore END DESC 

    INSERT INTO #LeadTable
	(
		LeadId,Createdon,CustId,StatusId,SubStatusId,IsAllocable,policyexpirydate,GroupID,Utm_Source
	)	
    SELECT			TOP 3000 LDParent.LeadID,LD.CreatedON,LD.CustomerID,ISNULL(LD.StatusID,1),ISNULL(LD.SubStatusID,0)
                        ,1 AS IsAllocable,NULL
                        ,3550,LD.Utm_source	                        	                        
    FROM            [Matrix].[CRM].[leaddetails150] LD  WITH(NOLOCK)
    INNER  JOIN     Matrix.CRM.leaddetails150 LDParent (NOLOCK) ON LD.ParentID=LDParent.LeadID	    
    LEFT   JOIN     [Matrix].[CRM].[LeadAssignDetails] LAD(NOLOCK) ON LAD.LeadID=LDParent.LeadID AND LAD.IsLastAssigned=1 
    AND             LAD.AssignToGroupId=3550
    LEFT   JOIN     CRM.InvalidMobileNumbers INV (NOLOCK) ON LD.CustomerID=INV.CustID		
    LEFT   JOIN     #LeadTable LT ON LT.LeadId=LDParent.LeadID
    WHERE           LD.CreatedON BETWEEN GETDATE()-30	AND GETDATE()
    AND				LD.ProductID = 139  AND  LDParent.ParentID IS NULL	
    AND				LD.statusID IN (1,2,3,4,11)
    AND				ISNULL(LD.Utm_source,'') IN ('RMEnquiry')			        
    AND				LAD.ID IS NULL	
    AND				INV.id IS NULL  
    AND             ISNULL(LD.StateID,0) <> 25 
    AND             LT.LeadId IS NULL
    AND             LD.CustomerID NOT IN (121966235,61698464,24981,285582,99850044,134607,107702374,424,34849,14660959,96653864,99761951,140,112423343,17556611,101303023,100381355,22939032,2497,21835920,112111305,117038488,80601553,35148309,1889)         
    ORDER BY        LD.LeadScore DESC   


    UPDATE       LT
    SET          LT.IsAllocable=1
    FROM         #LeadTable LT
    INNER JOIN
    (
        SELECT       LT.LeadId,COUNT(1) Selections
        FROM         #LeadTable LT 
        INNER JOIN   dbo.SelectedQuotes SQ ON LT.LeadId=SQ.LeadID   
        WHERE        LT.GroupID <> 3543    
        AND          LT.Utm_Source='whatsapp_crm_sales'    
		GROUP BY     LT.LeadId 
    ) A
    ON      LT.LeadId=A.LeadId
    WHERE   LT.GroupID <> 3543
    AND     LT.Utm_Source='whatsapp_crm_sales'

    UPDATE       LT
    SET          LT.IsAllocable=0
    FROM         #LeadTable LT
    WHERE        LT.GroupID=3543
    
    UPDATE       LT
    SET          LT.IsAllocable=1,LT.GroupID=3055
    FROM         #LeadTable LT
    INNER JOIN
    (
        SELECT       LT.LeadId,COUNT(1) Selections
        FROM         #LeadTable LT 
        INNER JOIN   dbo.SelectedQuotes SQ ON LT.LeadId=SQ.LeadID   
        WHERE        LT.GroupID=3543
        AND          (LT.policyexpirydate BETWEEN DATEADD(YEAR,-2,GETDATE()) AND CAST(GETDATE()+10 AS DATE))            
		GROUP BY     LT.LeadId 
    ) A
    ON      LT.LeadId=A.LeadId
    WHERE   LT.GroupID=3543

   
	/*Commercial Renewal*/	
	INSERT INTO #LeadTable
	(
		LeadId,LeadRank,Createdon,CustId,StatusId,SubStatusId,IsAllocable,policyexpirydate,GroupID
	)			
	SELECT			DISTINCT TOP 2000 CASE WHEN LD.ParentID > 0 THEN LD.ParentID ELSE LD.LeadID END
                    ,LDParent.LeadRank,LDParent.CreatedON,LDParent.CustomerID,LDParent.statusID,LDParent.SubStatusID,1
					,GETDATE()
					,3503					
	FROM            [Matrix].[CRM].[leaddetails150] LD  WITH(NOLOCK)		
    INNER JOIN      [Matrix].[CRM].[leaddetails150] LDParent (NOLOCK) ON LDParent.LeadID=CASE WHEN LD.ParentID > 0 THEN LD.ParentID ELSE LD.LeadID END
    INNER JOIN      CRM.CarDetails CD (NOLOCK) ON LD.LeadID=CD.LeadID AND CD.PreviousPolicyExpiryDate BETWEEN CAST(GETDATE() AS DATE) AND CAST(GETDATE()+40 AS DATE)
	LEFT JOIN       [Matrix].[CRM].[LeadAssignDetails] LAD(NOLOCK) ON LAD.LeadID=LDParent.LeadID AND LAD.IsLastAssigned=1 	
	WHERE           LD.CreatedON BETWEEN GETDATE()-30 AND GETDATE()
	AND				LD.ProductID = 139  
	AND				(LDParent.IsActive=1 OR LDParent.IsActive  IS NULL) 
    AND				ISNULL(LD.Utm_source,'') NOT IN ('OfflineAffiliate')
    AND             LD.LeadSource='RENEWAL'	
	AND				LD.StatusID IN (1,2,3,4,11)		
	AND				LAD.ID IS NULL
	ORDER BY        LDParent.CreatedON DESC

    INSERT INTO #LeadTable
	(
		LeadId,LeadRank,Createdon,CustId,StatusId,SubStatusId,IsAllocable,GroupID
	)	
    SELECT A.*
    FROM
    (
        SELECT			DISTINCT TOP 2000 LDParent.LeadID
                        ,CASE WHEN TWD.Segment=1 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource='PB' AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 60 AND 240 AND  TWD.VehiclePlanType IN (1,2) THEN 1 --Bike / comp
                              WHEN TWD.Segment=1 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource='PB' AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 84 AND 240 AND  TWD.VehiclePlanType IN (3) THEN 1 --Bike / TP
                              WHEN TWD.Segment=2 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource='PB' AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 60 AND 240 AND  TWD.VehiclePlanType IN (1,2) THEN 1 --SCooter / comp                              
                              WHEN TWD.Segment=2 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource='PB' AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 60 AND 180 AND  TWD.VehiclePlanType IN (3) THEN 1 --SCooter / TP

                              WHEN TWD.Segment=1 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource='PBMOBILE' AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 84 AND 180 AND  TWD.VehiclePlanType IN (1,2) THEN 2 --Bike / comp
                              WHEN TWD.Segment=1 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource IN ('PB','PBMOBILE') AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 60 AND 240 AND  TWD.VehiclePlanType IN (3) THEN 2 --Bike / TP
                              WHEN TWD.Segment=1 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource IN ('PB') AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 120 AND 240 THEN 2 --AND NonSelection THEN 1 --Bike / Non Selection
                              WHEN TWD.Segment=2 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource IN ('PB','PBMOBILE') AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 60 AND 240 AND  TWD.VehiclePlanType IN (3) THEN 2 --SC / comp
                              WHEN TWD.Segment=2 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource IN ('PB','PBMOBILE','PBMOBILEAPP') AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 60 AND 180 AND  TWD.VehiclePlanType IN (1,2) THEN 2 --SC / TP
                              WHEN TWD.Segment=2 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource IN ('PB') AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 60 AND 240  THEN 2--AND NonSelection THEN 1 --SC / NonSelection

                              WHEN TWD.Segment=1 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource IN ('PBMOBILE','PBMOBILEAPP') AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 60 AND 240 AND  TWD.VehiclePlanType IN (1,2) THEN 3 --Bike / comp
                              WHEN TWD.Segment=1 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource IN ('PBMOBILE','PBMOBILEAPP') AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 60 AND 240 AND  TWD.VehiclePlanType IN (3) THEN 3 --Bike / TP
                              WHEN TWD.Segment=1 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource IN ('PBMOBILE','PBMOBILEAPP') AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 60 AND 240 THEN 3 --AND NonSelection THEN 1 --Bike / Non Selection
                              WHEN TWD.Segment=2 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource IN ('PBMOBILE','PB','PBMOBILEAPP') AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 60 AND 240 AND  TWD.VehiclePlanType IN (3) THEN 3 --SC / comp
                              WHEN TWD.Segment=2 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource IN ('PBMOBILE') AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 180 AND 240 AND  TWD.VehiclePlanType IN (1,2) THEN 3 --SC / TP
                              WHEN TWD.Segment=2 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource IN ('PB','PBMOBILEAPP') AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 60 AND 240  THEN 3 --SC / non
                              WHEN TWD.Segment=2 AND DATEDIFF(DAY,TWD.PreviousPolicyExpiryDate,LD.CreatedON) > 90 AND LD.LeadSource IN ('PBMOBILE','PBMOBILEAPP') AND DATEDIFF(MONTH,TWD.RegistrationDate,LD.CreatedON) BETWEEN 60 AND 120 AND  TWD.VehiclePlanType IN (4,5) THEN 3 --SC / SAOD
                              
                              ELSE 4 END AS LeadRank

                        ,LDParent.CreatedON,LDParent.CustomerID,LDParent.statusID,LDParent.SubStatusID,1 AS IsAllocable					
                        --,CASE WHEN ISNULL(TWD.LeadErrorType,1)=1 THEN 2204 ELSE 2067 END AS GroupID                       
                        ,CASE WHEN LD.LeadSource='Crosssell' and LD.Utm_source='TwowheelerOnCorp' THEN 'MotorOnCorp'  WHEN TWD.LeadErrorType=12 THEN 2787 ELSE 2204 END AS GroupID
        FROM            [Matrix].[CRM].[leaddetails150] LD  WITH(NOLOCK)	
        INNER JOIN      MTX.TWVehicleDetails TWD (NOLOCK) ON LD.LeadID=TWD.MatrixLeadID 
        --AND             PGHitTime > CAST(GETDATE() AS DATE)	
        AND             ISNULL(TWD.LeadErrorType,1) IN (10,11,12) --(PGHitTime < DATEADD(MINUTE,-15,GETDATE()) OR ISNULL(TWD.LeadErrorType,1)=5)
        INNER JOIN      [Matrix].[CRM].[leaddetails150] LDParent (NOLOCK) ON LDParent.LeadID=CASE WHEN LD.ParentID > 0 THEN LD.ParentID ELSE LD.LeadID END                 
        LEFT JOIN       CRM.InvalidMobileNumbers INV ON LD.CustomerID=INV.CustID
        WHERE           LD.CreatedON BETWEEN GETDATE()-3 AND DATEADD(MINUTE,-120,GETDATE())
        AND				LD.ProductID = 114          
        AND				ISNULL(LD.Utm_source,'') NOT IN ('OfflineAffiliate','CV_Renewal','swarafincare')	
        AND             ISNULL(LDParent.Utm_source,'') NOT IN ('OfflineAffiliate','CV_Renewal','swarafincare')	--,'2W_Renewal_Outbound_45'
        AND				LD.StatusID IN (1,2,3,4,11)		              
        AND				LDParent.AssignedDate IS NULL
        AND             INV.id IS NULL
        ORDER BY        LDParent.LeadID DESC
    ) A ORDER BY A.LEADRANK 

    /*SOS abandon calls*/
    INSERT INTO #LeadTable
    (
        LeadId,LeadRank,Createdon,CustId,StatusId,SubStatusId,IsAllocable,GroupID
    )    
    SELECT			DISTINCT TOP 2000 LD.LeadID
                    ,0 AS LeadRank
                    ,LD.CreatedON,LD.CustomerID,LD.statusID,LD.SubStatusID,1 AS IsAllocable					                                        
                    ,2692 AS GroupID
    FROM            [Matrix].[CRM].[leaddetails150] LD  WITH(NOLOCK)	                                
	INNER JOIN      MTX.CallDataHistory CDH (NOLOCK) ON LD.leadID=CDH.LeadID AND CDH.CallType='IB'
    LEFT JOIN       [Matrix].[CRM].[LeadAssignDetails] LAD(NOLOCK) ON LAD.LeadID=LD.LeadID 
	AND 			LAD.IsLastAssigned=1 	    
    WHERE           LD.CreatedON BETWEEN GETDATE()-15 AND DATEADD(MINUTE,-120,GETDATE())
    AND				LD.ProductID = 219          
    AND				ISNULL(LD.Utm_source,'') NOT IN ('OfflineAffiliate','OnlineAffiliate')	
    AND				LD.StatusID IN (1,2,3,4,11)		              
    AND				LAD.ID IS NULL   

     /*Appointment verification Assignment*/
    INSERT INTO #LeadTable
    (
        LeadId,LeadRank,Createdon,CustId,StatusId,SubStatusId,IsAllocable,GroupID
    )    
    SELECT			DISTINCT TOP 2000 LD.LeadID
                    ,0 AS LeadRank
                    ,LD.CreatedON,LD.CustomerID,LD.statusID,LD.SubStatusID,1 AS IsAllocable					                                        
                    ,2886 AS GroupID
    FROM            [Matrix].[CRM].[leaddetails150] LD  WITH(NOLOCK)	                                	
    LEFT JOIN       [Matrix].[CRM].[LeadAssignDetails] LAD(NOLOCK) ON LAD.LeadID=LD.LeadID 
	AND 			LAD.IsLastAssigned=1 	    
    WHERE           LD.CreatedON BETWEEN GETDATE()-15 AND DATEADD(MINUTE,-1,GETDATE())
    AND				LD.ProductID = 220          
    AND				ISNULL(LD.Utm_source,'') IN ('AppVerification_Health')	
    AND				LD.StatusID IN (1,2,3,4,11)		              
    AND				LAD.ID IS NULL    

    /*stu Assignment
    INSERT INTO #LeadTable
    (
        LeadId,LeadRank,Createdon,CustId,StatusId,SubStatusId,IsAllocable,GroupID
    )    
    SELECT			DISTINCT TOP 2000 LD.LeadID
                    ,165 AS LeadRank
                    ,LD.CreatedON,LD.CustomerID,LD.statusID,LD.SubStatusID,1 AS IsAllocable					                                        
                    ,2903 AS GroupID
    FROM            [Matrix].[CRM].[leaddetails150] LD  WITH(NOLOCK)	                                	
    LEFT JOIN       [Matrix].[CRM].[LeadAssignDetails] LAD(NOLOCK) ON LAD.LeadID=LD.LeadID 
	AND 			LAD.IsLastAssigned=1 	    
    WHERE           LD.CreatedON BETWEEN GETDATE()-15 AND DATEADD(MINUTE,-1,GETDATE())
    AND				LD.ProductID = 118
    AND             LD.LeadSource IN ('PBHLTPA','PBMOBILEHLTPA','PBMOBILEAPPHLTPA')          
    --AND				ISNULL(LD.Utm_source,'') IN ('AppVerification_Health')	
    AND				LD.StatusID IN (1,2,3,4,11)		              
    AND				LAD.ID IS NULL */   

    /*PA AND STU Assignment*/
    INSERT INTO #LeadTable
    (
        LeadId,LeadRank,Createdon,CustId,StatusId,SubStatusId,IsAllocable,GroupID
    )    
    SELECT			DISTINCT TOP 5000 LD.LeadID
                    ,165--CASE WHEN LD.Utm_term LIKE '%IOS%' OR (LD.DateOfBirth IS NOT NULL AND DATEDIFF(YEAR,LD.DateOfBirth,CAST(GETDATE() AS DATE)) BETWEEN 35 AND 60) THEN 165 ELSE 166 END AS LeadRank
                    ,LD.CreatedON,LD.CustomerID,LD.statusID,LD.SubStatusID,1 AS IsAllocable					                                        
                    ,2903 AS GroupID
    FROM            [Matrix].[CRM].[leaddetails150] LD  WITH(NOLOCK)	                                	
    LEFT JOIN       [Matrix].[CRM].[LeadAssignDetails] LAD(NOLOCK) ON LAD.LeadID=LD.LeadID 
	AND 			LAD.IsLastAssigned=1 	    
    WHERE           LD.CreatedON BETWEEN GETDATE()-15 AND DATEADD(MINUTE,-1,GETDATE())
    AND				(
                           --(LD.ProductID = 118 AND LD.LeadSource IN ('PBHLTPA','PBMOBILEHLTPA','PBMOBILEAPPHLTPA'))
                        --OR 
                            (LD.ProductID = 130 AND LD.LeadSource IN ('PBMOBILEAPP') AND LD.Utm_Medium LIKE '%stu%')
                        OR (LD.ProductID = 130 AND LD.LeadSource IN ('PB','PBMOBILE') AND LD.utm_content IN ('stu_icon_hp_stu_dom','stu_icon_hp_stu_NRI'))
                    )              
    AND				ISNULL(LD.Utm_source,'') NOT IN ('OfflineAffiliate','OnlineAffiliate')	
    AND				LD.StatusID IN (1,2,3,4,11)		
    AND             LD.ParentID IS NULL              
    AND				LAD.ID IS NULL   
    ORDER BY        LD.LeadID DESC 

    /*extended warranty assignment*/
    INSERT INTO #LeadTable
    (
        LeadId,LeadRank,Createdon,CustId,StatusId,SubStatusId,IsAllocable,GroupID
    )    
    SELECT			DISTINCT TOP 2000 LD.LeadID
                    ,0 AS LeadRank
                    ,LD.CreatedON,LD.CustomerID,LD.statusID,LD.SubStatusID,1 AS IsAllocable					                                        
                    ,3424 AS GroupID
    FROM            [Matrix].[CRM].[leaddetails150] LD  WITH(NOLOCK)	                                	
    LEFT JOIN       [Matrix].[CRM].[LeadAssignDetails] LAD(NOLOCK) ON LAD.LeadID=LD.LeadID 
	AND 			LAD.IsLastAssigned=1 	    
    WHERE           LD.CreatedON > CAST(GETDATE()-5 AS DATE)
    AND             LD.ProductID=222             
    AND				ISNULL(LD.Utm_source,'') NOT IN ('OfflineAffiliate','OnlineAffiliate')	
    AND				LD.StatusID IN (1,2,3,4,11)		
    AND             LD.ParentID IS NULL              
    AND				LAD.ID IS NULL   
    ORDER BY        LD.LeadID DESC 
    	
	
    /*update booking on parent lead*/
	UPDATE		LT
    SET			LT.IsAllocable=0
    FROM		#LeadTable LT    
    INNER JOIN	Matrix.MTX.BookingDetails BD (NOLOCK) ON LT.LeadID=BD.LEADID AND BD.PaymentSTATUS in(300,3002,4002,5002,6002)  
	WHERE 		ISNULL(LT.GroupID,0) NOT IN  (2692,3550)
    
	/*update booking on Child lead*/
    UPDATE		LT
    SET			LT.IsAllocable=0
    FROM		#LeadTable LT
    INNER JOIN  [Matrix].[CRM].[Leaddetails] LD (NOLOCK) 
	ON			LD.ParentID=LT.LeadId
    INNER JOIN	Matrix.MTX.BookingDetails BD (NOLOCK) ON LD.LeadID=BD.LEADID AND BD.PaymentSTATUS in(300,3002,4002,5002,6002)    
	WHERE 		ISNULL(LT.GroupID,0) NOT IN  (2692,3550)
	
	UPDATE		LT
    SET			LT.IsAllocable=0
    FROM		#LeadTable LT 
    INNER JOIN  [Matrix].[MTX].[CustomerUnsubscription] PC (NOLOCK)
	ON			PC.CustomerId=LT.CustId AND CategoryId=1 AND ChannelId=3 AND IsActive=1   		      
	WHERE 		ISNULL(LT.GroupID,0) NOT IN  (2692)
	
---------------------------------------------------------------------------------------------------------------------------------------------
---------------Obtaining all term agents for lead allocation along with their Lead Count and PPL Value-----------------	  
	
	DECLARE @AgentTable AS TABLE
	(
		 [AgentID] [BIGINT] NULL,[AgentCode] [varchar](30) NULL,[GroupId] [int] NULL,[DailyLimit] [int] NULL,[BucketSize] [smallint] NULL
		,[AssignedLeadsCount] [smallint] NULL,[OpenLeadCount] [int] NULL,[Grade] SMALLINT,[batchallocatedlead] TINYINT
	)	
		
	INSERT INTO	@AgentTable 
	(
		 [AgentID],[AgentCode],[GroupId],[DailyLimit],[BucketSize],[AssignedLeadsCount],OpenLeadCount,Grade,[batchallocatedlead]
	)
    SELECT		ugrn.UserId AS UserID,UD.EmployeeId,UGRN.GroupId,ud.limit,ud.BucketSize,LDTS.Counter,UD.OpenLeadCount,ud.Grade,0
	FROM		CRM.UserGroupRoleMapNew AS UGRN WITH(NOLOCK) 
	INNER JOIN  CRM.UserDetails ud WITH(NOLOCK) ON ud.UserID=UGRN.UserId AND ud.IsActive=1 AND UGRN.GroupId IN (2204,2692,2787,2886,2903,3055,3409,3424,3542,3550,3503,3543)
	INNER JOIN	CRM.LoginDetails LDTS (NOLOCK) ON LDTS.UserID=ugrn.UserId AND LDTS.CreatedOn BETWEEN @FromLoginDate AND @ToLoginDate AND LDTS.Active=1 
	INNER JOIN  CRM.PermissionDetails PD (NOLOCK) ON UGRN.UserId=PD.UserId  AND PD.ProcessId IN (1,2) AND UGRN.RoleSuperId=11 AND UGRN.AutoAllocation=1						
	
	--UPDATE @AgentTable SET DailyLimit=	DailyLimit*2 WHERE AgentID=12206
        
---------------------------------------------------------------------------------------------------------------------------------------------
----------------------------------- Start allocating leads ------------------------------------------------------
	DECLARE @TotalLeadCount BIGINT=0        
	DECLARE @Counter BIGINT=1        
	DECLARE @LeadId BIGINT        	
	DECLARE @LeadRank SMALLINT   	
	Declare @InsurerId int
	DECLARE @CustID BIGINT 
	DECLARE @RenewalYear INT
	DECLARE @UserID BIGINT
	DECLARE @GroupId SmallInt = 703
	DECLARE @AssignedGroupId SmallInt = 0
	Declare @AgentGrade Int
	Declare @LeadStatusId TinyInt
	Declare @LeadSubStatusId SmallInt
	DECLARE @IsAllocable BIT=0
	DECLARE @PolicyExpiryDate DATE=null
	SELECT @TotalLeadCount = COUNT(1) FROM #LeadTable 
	IF @TotalLeadCount>0         
	 BEGIN        		 		  
		  WHILE @Counter <= @TotalLeadCount        
			  BEGIN  			
						SET @IsAllocable=1		
						SET @UserID=null			
						SET @PolicyExpiryDate=null	
						SET @GroupId=0	
						SET @AssignedGroupId=0		
                        SET @LeadRank=0
						SELECT  @LeadId = LeadId,@LeadRank = LeadRank,@CustID = CustId,@LeadStatusId = StatusId,@LeadSubStatusId =SubStatusId
						,@IsAllocable=IsAllocable,@policyexpirydate=CAST(policyexpirydate AS DATE),@AssignedGroupId=GroupID
						FROM    #LeadTable WHERE ID = @Counter
						
						IF @IsAllocable=1
							BEGIN                                
                                IF @AssignedGroupId=2903 AND @LeadRank=166
                                    BEGIN
                                        EXEC [CRM].[Insert_AssignedToAgent] NULL ,124,2,@LeadId,@AssignedGroupId ,1,3,@InsurerId,0,@LeadRank
                                    END    
								ELSE IF @AssignedGroupId IN (2204) 
									BEGIN
											SELECT			TOP 1 @UserID= AgentID,@GroupId=AT.GroupId,@AgentGrade=AT.Grade 
											FROM			@AgentTable AT	
											INNER JOIN      [Matrix].[CRM].[leaddetails150] LD (NOLOCK) ON AT.AgentID=LD.assignedTO										
											WHERE			LD.CustomerID=@CustID AND LD.ProductID=114 AND LD.CreatedON > GETDATE()-30
											AND             LD.ParentID IS NULL
											ORDER BY        LD.LeadID DESC
											IF ISNULL(@UserID,0)=0
												BEGIN
													SELECT			TOP 1 @UserID= AgentID,@GroupId=AT.GroupId,@AgentGrade=AT.Grade 
													FROM			@AgentTable AT	
													--INNER JOIN      [MTX].[LeadAgentRankMapping_NewApp] LARM (NOLOCK)  
													--ON              LARM.AgentRank = AT.Grade AND @LeadRank = LARM.LeadRank AND LARM.ProductID=114 AND Coeff > 0								
													WHERE			AT.DailyLimit > AT.AssignedLeadsCount	 
													AND				AT.BucketSize > AT.OpenLeadCount
													AND				AT.batchallocatedlead <  CASE WHEN @AssignedGroupId=2903 THEN 6 ELSE 3 END 
													AND				AT.GroupId =@AssignedGroupId
													ORDER BY        AT.AssignedLeadsCount
												END
									END
								ELSE IF @AssignedGroupId > 0
									BEGIN
										SELECT			TOP 1 @UserID= AgentID,@GroupId=AT.GroupId,@AgentGrade=AT.Grade 
										FROM			@AgentTable AT											
										WHERE			AT.DailyLimit > AT.AssignedLeadsCount	 
										AND				AT.BucketSize > AT.OpenLeadCount
										AND				AT.batchallocatedlead <  CASE WHEN @AssignedGroupId=3550 THEN 300 ELSE 1 END 
										AND				AT.GroupId =@AssignedGroupId
										ORDER BY        AT.AssignedLeadsCount
									END                                                                                    																																																																																																					
								
								IF ISNULL(@UserID,0) > 0
									BEGIN								  										
										EXEC [CRM].[Insert_AssignedToAgent] @UserID ,124,2,@LeadId,@GroupID ,1,21,@InsurerId,0,@LeadRank
																									
										UPDATE  CRM.LoginDetails 
										SET		Counter=Counter+1 																	
										WHERE	UserID=@UserID AND CreatedON BETWEEN @FromLoginDate AND @ToLoginDate
											
										UPDATE  @AgentTable 
										SET		AssignedLeadsCount=ISNULL(AssignedLeadsCount,0)+1,batchallocatedlead=batchallocatedlead+1 																	
										WHERE	AgentID=@UserID 														
									END																							
									
							END	
						
						SET @Counter=@Counter+1													
			  END			  			  			  		  
	END			    
	DROP TABLE #LeadTable																				
END




