﻿CREATE PROCEDURE [MTX].[AllowedDuplicateLeadSearch]
(   @SearchText VARCHAR(20),
	@SearchType int,
    @UserID BIGINT,
    @RoleId INT,
    @SearchPage VARCHAR(100) = '',
    @IsSearchAllowed BIT OUTPUT,
	@CustomerIdOut BIGINT OUTPUT,
    @IsNRICust BIT OUTPUT)
AS
BEGIN

    SET NOCOUNT ON;
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
	SET DEADLOCK_PRIORITY LOW  

    IF OBJECT_ID('tempdb..#TEMP') IS NOT NULL  
    DROP TABLE #TEMP 

	BEGIN TRY

	    SET @SearchText = LTRIM(RTRIM(@SearchText));
	    SET @SearchText = CAST(@SearchText AS BIGINT);

        DECLARE @type VARCHAR(20)='search';
		DECLARE @CustomerID BIGINT = 0;
		DECLARE @IsOtpAlreadyVerified BIT = 0;
		DECLARE @SearchCount INT = 0;
		DECLARE @SearchTypeTemp VARCHAR(100) = '';
		CREATE TABLE #TEMP(CUSTOMERID BIGINT, IsNRICust BIT)

        IF(@RoleId = 13 AND @SearchPage != 'SearchLeads')
        BEGIN
            --CHECKING FOR LEADID
            IF(@SearchType = 0)
            BEGIN
                SELECT TOP 1 @CustomerID = LD.CustomerID
                FROM [Matrix].[CRM].[Leaddetails] LD WITH (NOLOCK)
                WHERE LD.LeadID = @SearchText
                ORDER BY CreatedON DESC

                IF EXISTS(Select top(1) 1  from CRM.InvalidMobileNumbers with(nolock) 
                where CustID=@customerId and UpdatedBy  is null and IsActive=1)
                BEGIN	
		        	SET @CustomerIdOut = 0
		        	SET @IsSearchAllowed = 1;
		        	SET @IsNRICust=0;			
		        	RETURN;
		        END
            END
            -- Check FOR the mobile number 
	        ELSE IF(@SearchType = 1)
	        BEGIN

	            IF EXISTS(Select top(1) 1  from CRM.InvalidMobileNumbers with(nolock) 
                 WHERE MobileNo=@SearchText and UpdatedBy is null and IsActive=1) 		
	            /*In case of Invalid mobileNo. return from here*/
	            BEGIN	
	            	SET @CustomerIdOut = 0
	            	SET @IsSearchAllowed = 1;
	            	SET @IsNRICust=0;			
	            	RETURN;
	            END

                SELECT TOP 1 @CustomerID = LD.CustomerID
                FROM [Matrix].[CRM].[Leaddetails] LD WITH (NOLOCK)
                WHERE LD.MobileNo = @SearchText
                ORDER BY CreatedON DESC
	        END

            IF EXISTS(SELECT TOP 1 1 FROM CRM.UserGroupRoleMapNew WHERE UserId = @UserID AND GroupId IN (1256))
            BEGIN
                --Checking if Otp is verified by agent or not
                IF EXISTS (SELECT TOP 1 1 FROM mtx.DuplicateLeadsOtpTracking WHERE CustomerID = @CustomerID and UserID = @UserId 
	    	            AND  VerifiedOn  IS NOT NULL and VerifiedOn > DATEADD(HOUR,-1,GETDATE()) AND [Type] = 'booking' ) 
                BEGIN
                    SET @IsOtpAlreadyVerified = 1;
                END

                --If OTP is not already verified
                IF(@IsOtpAlreadyVerified = 0 AND @CustomerID > 0)
                BEGIN
                    INSERT INTO #TEMP(CUSTOMERID, IsNRICust)
                    SELECT TOP 1 LD.CustomerID,
                                 CASE 
                                    WHEN LD.Country IN (
	    	        			''
	    	        			,'0'
	    	        			,'91'
	    	        			,'+91'
	    	        			,'INDIA'
	    	        			,'392'
	    	        			)
	    	        		        THEN 0
                                    ELSE 1
                                END
                    FROM MATRIX.CRM.Leaddetails LD WITH(NOLOCK)
                    INNER JOIN MATRIX.MTX.BookingDetails BD WITH(NOLOCK) ON BD.LEADID = LD.LeadID
                    INNER JOIN CRM.PaymentModeMaster PMM WITH(NOLOCK) ON PMM.PaymentModeValue = BD.PaymentStatus AND PMM.ParentModeValue = 0
                    WHERE LD.CustomerID = @CustomerID

                    --IF CUSTOMERID DOES NOT EXISTS THEN RETURN
                    IF EXISTS(SELECT TOP 1 1 FROM #TEMP)
                    BEGIN
	    	    	    SET @type='booking';
                        SET @CustomerIdOut = @CustomerID
                        SET @IsSearchAllowed = 0;
                        SELECT @IsNRICust = IsNRICust FROM #TEMP;
                    END
                    --IF NOT THEN CHECK FOR PREVIOUS INPUTS, ELSE CHECK FOR OTP
                    ELSE 
                    BEGIN
                        SET @IsSearchAllowed = 1;
                        SET @CustomerIdOut = 0;
                        SELECT @IsNRICust = 0;
                    END
                END
                --If verified already
                ELSE
                BEGIN
                    SET @IsSearchAllowed = 1;
                    SET @CustomerIdOut = 0;
                    SET @IsNRICust = 0;
                END
            END
            ELSE
            BEGIN
                SET @IsSearchAllowed = 1;
                SET @CustomerIdOut = 0;
                SET @IsNRICust = 0;
            END
        END
        ELSE
        BEGIN
            --CHECKING FOR LEADID
            IF(@SearchType = 0)
            BEGIN
                SELECT TOP 1 @CustomerID = LD.CustomerID
                FROM [Matrix].[CRM].[Leaddetails] LD WITH (NOLOCK)
                WHERE LD.LeadID = @SearchText
                ORDER BY CreatedON DESC

                IF EXISTS(Select top(1) 1  from CRM.InvalidMobileNumbers with(nolock) 
                where CustID=@customerId and UpdatedBy  is null and IsActive=1)
                BEGIN	
	    	    	SET @CustomerIdOut = 0
	    	    	SET @IsSearchAllowed = 1;
	    	    	SET @IsNRICust=0;			
	    	    	RETURN;
	    	    END
            END
            -- Check FOR the mobile number 
	        ELSE IF(@SearchType = 1)
	        BEGIN

                IF EXISTS(Select top(1) 1  from CRM.InvalidMobileNumbers with(nolock) 
                 WHERE MobileNo=@SearchText and UpdatedBy  is null and IsActive=1) 		
	    	    /*In case of Invalid mobileNo. return from here*/
	    	    BEGIN	
	    	    	SET @CustomerIdOut = 0
	    	    	SET @IsSearchAllowed = 1;
	    	    	SET @IsNRICust=0;			
	    	    	RETURN;
	    	    END

                SELECT TOP 1 @CustomerID = LD.CustomerID
                FROM [Matrix].[CRM].[Leaddetails] LD WITH (NOLOCK)
                WHERE LD.MobileNo = @SearchText
                ORDER BY CreatedON DESC
	        END

            IF(@CustomerID > 0)
            BEGIN
                INSERT INTO #TEMP(CUSTOMERID, IsNRICust)
                SELECT TOP 1 LD.CustomerID,
                         CASE 
                            WHEN LD.Country IN (
	    				''
	    				,'0'
	    				,'91'
	    				,'+91'
	    				,'INDIA'
	    				,'392'
	    				)
	    			        THEN 0
                            ELSE 1
                        END
                FROM MATRIX.CRM.Leaddetails LD WITH(NOLOCK)
                INNER JOIN MATRIX.MTX.BookingDetails BD WITH(NOLOCK) ON BD.LEADID = LD.LeadID
                INNER JOIN CRM.PaymentModeMaster PMM WITH(NOLOCK) ON PMM.PaymentModeValue = BD.PaymentStatus AND PMM.ParentModeValue = 0
                WHERE LD.CustomerID = @CustomerID
            END
    
	    		--IF CUSTOMERID DOES NOT EXISTS THEN RETURN
            IF EXISTS(SELECT TOP 1 1 FROM #TEMP)
            BEGIN
	    	   SET @type='booking';
                --Counting no of searches made in past hour
                SET @SearchCount = (SELECT COUNT(DISTINCT CustomerID) 
	    		FROM mtx.DuplicateLeadsOtpTracking WHERE UserID = @UserId AND CreatedOn > DATEADD(HOUR,-1,GETDATE()) AND [Type] = 'booking')     
            END

            IF(@SearchCount < 150)
            BEGIN
                SET @IsSearchAllowed = 1;
                SET @CustomerIdOut = @CustomerID;
                SET @IsNRICust = 0;
            END
            ELSE
            BEGIN
                SET @CustomerIdOut = @CustomerID;
                SET @IsSearchAllowed = 0;
                SET @IsNRICust = 0
            END
        END 
    END TRY
	BEGIN CATCH

	END CATCH 

    SELECT @SearchTypeTemp = CASE WHEN @SearchType = 0 THEN 'By LeadId'
                                  WHEN @SearchType = 1 THEN 'By Mobile' END
	
    INSERT INTO mtx.DuplicateLeadsOtpTracking(CustomerID,UserID,[Type], CreatedOn, SearchPage, SearchText, SearchType)
    VALUES(@CustomerID,@UserId,@type, GETDATE(), @SearchPage, @searchText, @SearchTypeTemp)

    DROP TABLE #TEMP
END
GO
