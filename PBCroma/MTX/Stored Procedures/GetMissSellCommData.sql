﻿-------------------------------------------
--CREATED BY - Bhavesh
--CreatedOn - 10/10/2024
-------------------------------------------
-- exec [MTX].[GetMissSellCommData] 
ALTER   PROCEDURE [MTX].[GetMissSellCommData] 
(
    @EmployeeId VARCHAR(255),
    @ProductId INT = 2
)
AS 
BEGIN
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    SET DEADLOCK_PRIORITY LOW;
    SET NOCOUNT ON;

    DECLARE @IncentiveMonth DATETIME = GETDATE() + 90;

    SELECT @IncentiveMonth = MAX(IncentiveMonth) FROM MTX.AgentProcessDetails WHERE ISACTIVE = 1 AND PRODUCTID = @PRODUCTID

    SELECT  top(1) TLEmployeeId, TLUserName, UDTL.Email AS TLEmail,
            AMEmployeeId, AMUserName, UDAM.Email AS AMEmail,
            ManagerEmployeeId, ManagerUserName, UDM.Email AS ManagerEmail
    FROM mtx.AgentProcessDetails APD WITH(NOLOCK)
    INNER JOIN CRM.UserDetails UDTL WITH(NOLOCK) ON UDTL.EmployeeId = APD.TLEmployeeId
    INNER JOIN CRM.UserDetails UDAM WITH(NOLOCK) ON UDAM.EmployeeId = APD.AMEmployeeId
    INNER JOIN CRM.UserDetails UDM WITH(NOLOCK) ON UDM.EmployeeId = APD.ManagerEmployeeId
    WHERE APD.EMPLOYEEID = @EMPLOYEEID AND APD.ISACTIVE = 1  AND APD.PRODUCTID = @PRODUCTID AND APD.INCENTIVEMONTH >= @INCENTIVEMONTH order by  APD.Id desc
END
GO
