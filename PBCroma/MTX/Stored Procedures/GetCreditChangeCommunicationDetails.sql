--EXEC MTX.GetCreditChangeCommunicationDetails 44902550,1,101257,2,4,1
CREATE PROCEDURE [MTX].[GetCreditChangeCommunicationDetails]
(
	@BookingID BIGINT,
	@AgentTypeID INT,
	@UserId BIGINT,
	@RequestID INT = 0,
	@CurrentStatus INT = 0

)
AS
BEGIN

SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
SET DEADLOCK_PRIORITY LOW
--SET NOCOUNT ON;

	--Remarks at each level/ Agent type with reason from reason master

	CREATE TABLE #TempCommDetails(BookingID BIGINT,	AgentType VARCHAR(20), Reason VARCHAR(500),
	RequestorRemarks VARCHAR(500),FirstApproverRemarks VARCHAR(500),SecondLevelRemarks VARCHAR(500),
	CurrentUserEmailID VARCHAR(100),CurrentUserName VARCHAR (100),CurrentUserEmpCode VARCHAR(20) , 
	RequestedByUserID BIGINT,RequestedByEmailID VARCHAR(100),RequestedByName VARCHAR (100),RequestedByEmpCode VARCHAR(20) ,
	FAUserID BIGINT,FAEmailID VARCHAR(100),FAName VARCHAR (100),FAEmpCode VARCHAR(20) ,
	OldTLEmailID VARCHAR(100),OldTLName VARCHAR (100),OldTLEmpCode VARCHAR(20) ,
	OldAMEmailID VARCHAR(100),OldAMName VARCHAR (100),OldAMEmpCode VARCHAR(20) ,
	NewTLEmailID VARCHAR(100),NewTLName VARCHAR (100),NewTLEmpCode VARCHAR(20) ,
	NewAMEmailID VARCHAR(100),NewAMName VARCHAR (100),NewAMEmpCode VARCHAR(20)
	)

    CREATE TABLE #BUDetails(BUEmpCode VARCHAR(255), BUUserName VARCHAR(255), BUEmail VARCHAR(255))

	DECLARE @OldAdvisorID BIGINT, @NewAdvisorID BIGINT,@OldTLEmployeeID varchar(20), @OldAMEmployeeID varchar(20), 
	@NewTLEmployeeID VARCHAR(20), @NewAMEmployeeID VARCHAR(20),@BUProductId INT, @ReasonId INT, @LeadProductId INT;

	select @OldAdvisorID = OldAdvisorUserID , @NewAdvisorID = NewAdvisorUserID, @ReasonId = ReasonID, @LeadProductId = ProductId
	FROM MTX.CreditChangeRequests CCR
	WHERE CCR.BookingId = @BookingID AND CCR.AgentTypeID = @AgentTypeID AND ((@CurrentStatus > 1 and CCR.RequestID = @RequestID) OR (@CurrentStatus = 1 AND CCR.IsActive = 1) OR (@CurrentStatus = 1 AND CCR.RequestID = @RequestID));

	INSERT INTO #TempCommDetails(BookingID,AgentType,Reason,RequestorRemarks,FirstApproverRemarks,SecondLevelRemarks,
	CurrentUserEmailID,CurrentUserName,CurrentUserEmpCode, 
	RequestedByUserID,RequestedByEmpCode,RequestedByName,RequestedByEmailID,
	FAUserID,FAEmailID ,FAName,FAEmpCode)
	SELECT BookingID,AgentType,CCRM.Reason,CCR.RequestorComment,CCR.FirstLevelComment,CCR.SecondLevelComment,
	UD2.Email,UD2.UserName,UD2.EmployeeId, 
	RequestedByUserID, UD.EmployeeId,UD.UserName,UD.Email,
	FirstLevelApproverUserID, UD1.Email,UD1.UserName,UD1.EmployeeId
	FROM MTX.CreditChangeRequests CCR 
	INNER JOIN MTX.CreditChangeReasonMaster CCRM ON CCRM.ReasonID = CCR.ReasonID
	INNER JOIN CRM.UserDetails UD ON UD.USERID = RequestedByUserID
	INNER JOIN CRM.UserDetails UD1 ON UD1.USERID= ISNULL(FirstLevelApproverUserID,124)
	INNER JOIN CRM.UserDetails UD2 ON UD2.USERID= @UserId
	WHERE CCR.BookingId = @BookingID AND CCR.AgentTypeID = @AgentTypeID AND ((@CurrentStatus > 1 and CCR.RequestID = @RequestID) OR (@CurrentStatus = 1 AND CCR.IsActive = 1) OR (@CurrentStatus = 1 AND CCR.RequestID = @RequestID));

	DECLARE @HealthMaxIncentiveMonth DATETIME='';
	SET @HealthMaxIncentiveMonth = (SELECT MAX(IncentiveMonth) from mtx.AgentProcessDetails
	WHERE productid = 2 and isactive = 1);

	DECLARE @HOMMaxIncentiveMonth DATETIME='';
	SET @HOMMaxIncentiveMonth = (SELECT MAX(IncentiveMonth) from mtx.AgentProcessDetails
	WHERE productid = 117 AND FloorProcess = 'HOM' and isactive = 1 );

	DECLARE @HealthRenewalMaxIncentiveMonth DATETIME='';
	SET @HealthRenewalMaxIncentiveMonth = (SELECT MAX(IncentiveMonth) from mtx.AgentProcessDetails
	WHERE productid = 147 and isactive = 1 );

	DECLARE @MotorIncentiveMonth DATETIME = '';
	SET @MotorIncentiveMonth = (SELECT MAX(IncentiveMonth) from mtx.AgentProcessDetails
	WHERE productid = 117 AND FloorProcess <> 'HOM' and isactive = 1 );

    DECLARE @MotorRenewalMaxIncentiveMonth DATETIME='';
	SET @MotorRenewalMaxIncentiveMonth = (SELECT MAX(IncentiveMonth) from mtx.AgentProcessDetails
	WHERE productid = 217 and isactive = 1 );

	DECLARE @TermMaxIncentiveMonth DATETIME='';
	SET @TermMaxIncentiveMonth = (SELECT MAX(IncentiveMonth) from mtx.AgentProcessDetails
	WHERE productid = 7 and isactive = 1 );

	DECLARE @InvestmentMaxIncentiveMonth DATETIME='';
	SET @InvestmentMaxIncentiveMonth = (SELECT MAX(IncentiveMonth) from mtx.AgentProcessDetails
	WHERE productid = 115 and isactive = 1 );

	--Old AM and TL details for health
	UPDATE TCD
	SET TCD.OldTLName = APD.TLUserName, TCD.OldTLEmpCode = APD.TLEmployeeId,  TCD.OldTLEmailID = UD.Email,
	TCD.OldAMName = APD.AMUserName, TCD.OldAMEmpCode = APD.AMEmployeeId, TCD.OldAMEmailID = UD1.Email
	FROM #TempCommDetails TCD
	INNER JOIN MTX.AgentProcessDetails APD ON APD.AGENTID = @OldAdvisorID
	INNER JOIN CRM.UserDetails UD ON UD.EmployeeId = APD.TLEmployeeId
	INNER JOIN CRM.UserDetails UD1 ON UD1.EmployeeId = APD.AMEmployeeId
	WHERE APD.IncentiveMonth >= CASE WHEN ProductId = 2 THEN @HealthMaxIncentiveMonth
                                     WHEN ProductId = 147 THEN @HealthRenewalMaxIncentiveMonth
									 WHEN ProductId = 117 AND FloorProcess = 'HOM' THEN @HOMMaxIncentiveMonth
                                     END
    AND APD.IsActive = 1
    
	--New AM and TL details for health
	UPDATE TCD
	SET TCD.NewTLName = APD.TLUserName, TCD.NewTLEmpCode = APD.TLEmployeeId,  TCD.NewTLEmailID = UD.Email,
	TCD.newAMName = APD.AMUserName, TCD.NewAMEmpCode = APD.AMEmployeeId, TCD.NewAMEmailID = UD1.Email
	FROM #TempCommDetails TCD
	INNER JOIN MTX.AgentProcessDetails APD ON APD.AGENTID = @NewAdvisorID
	INNER JOIN CRM.UserDetails UD ON UD.EmployeeId = APD.TLEmployeeId
	INNER JOIN CRM.UserDetails UD1 ON UD1.EmployeeId = APD.AMEmployeeId
	WHERE APD.IncentiveMonth >= CASE WHEN ProductId = 2 THEN @HealthMaxIncentiveMonth
                                     WHEN ProductId = 147 THEN @HealthRenewalMaxIncentiveMonth
									 WHEN ProductId = 117 AND FloorProcess = 'HOM' THEN @HOMMaxIncentiveMonth
                                     END
    AND APD.IsActive = 1

	--Updating AM details to manager details for Motor,Term and investment
	--Old AM and TL details
	UPDATE TCD
	SET TCD.OldTLName = APD.TLUserName, TCD.OldTLEmpCode = APD.TLEmployeeId,  TCD.OldTLEmailID = UD.Email,
	TCD.OldAMName = APD.ManagerUserName, TCD.OldAMEmpCode = APD.ManagerEmployeeId, TCD.OldAMEmailID = UD1.Email
	FROM #TempCommDetails TCD
	INNER JOIN MTX.AgentProcessDetails APD ON APD.AGENTID = @OldAdvisorID
	INNER JOIN CRM.UserDetails UD ON UD.EmployeeId = APD.TLEmployeeId
	INNER JOIN CRM.UserDetails UD1 ON UD1.EmployeeId = APD.ManagerEmployeeId
	WHERE APD.IncentiveMonth >= CASE WHEN ProductId = 117 AND FloorProcess <> 'HOM' THEN @MotorIncentiveMonth
                                     WHEN ProductId = 217 THEN @MotorRenewalMaxIncentiveMonth
									 WHEN ProductId = 7 THEN @TermMaxIncentiveMonth
									 WHEN ProductId = 115 THEN @InvestmentMaxIncentiveMonth
                                     END
    AND APD.IsActive = 1
    
	--New AM and TL details
	UPDATE TCD
	SET TCD.NewTLName = APD.TLUserName, TCD.NewTLEmpCode = APD.TLEmployeeId,  TCD.NewTLEmailID = UD.Email,
	TCD.newAMName = APD.ManagerUserName, TCD.NewAMEmpCode = APD.ManagerEmployeeId, TCD.NewAMEmailID = UD1.Email
	FROM #TempCommDetails TCD
	INNER JOIN MTX.AgentProcessDetails APD ON APD.AGENTID = @NewAdvisorID
	INNER JOIN CRM.UserDetails UD ON UD.EmployeeId = APD.TLEmployeeId
	INNER JOIN CRM.UserDetails UD1 ON UD1.EmployeeId = APD.ManagerEmployeeId
	WHERE APD.IncentiveMonth >= CASE WHEN ProductId = 117 AND FloorProcess <> 'HOM' THEN @MotorIncentiveMonth
                                     WHEN ProductId = 217 THEN @MotorRenewalMaxIncentiveMonth
									 WHEN ProductId = 7 THEN @TermMaxIncentiveMonth
									 WHEN ProductId = 115 THEN @InvestmentMaxIncentiveMonth
                                     END
    AND APD.IsActive = 1


	SELECT * FROM #TempCommDetails

    SELECT @BUProductId = PRODUCTID FROM MTX.AgentProcessDetails WHERE AgentId = @NewAdvisorID AND IsActive = 1 
                                        AND IncentiveMonth >= CASE WHEN ProductId = 2 THEN @HealthMaxIncentiveMonth
                                                                   WHEN ProductId = 147 THEN @HealthRenewalMaxIncentiveMonth
                                                                   WHEN ProductId = 117 AND FloorProcess = 'HOM' THEN @HOMMaxIncentiveMonth
                                                                   WHEN ProductId = 117 AND FloorProcess <> 'HOM' THEN @MotorIncentiveMonth
                                                                   WHEN ProductId = 217 THEN @MotorRenewalMaxIncentiveMonth
																   WHEN ProductId = 7 THEN @TermMaxIncentiveMonth
									 							   WHEN ProductId = 115 THEN @InvestmentMaxIncentiveMonth
                                                                   END

    INSERT INTO #BUDetails(BUEmpCode, BUUserName, BUEmail)
    SELECT UD.EmployeeId AS BUEmpCode,UD.UserName AS BUUserName,UD.Email As BUEmail
	FROM MTX.CreditChangeApproverMapping CCAM
	INNER JOIN CRM.UserDetails UD ON UD.UserID = CCAM.UserID 
	WHERE CCAM.AgentType = @AgentTypeID AND CCAM.ProductID = @BUProductId AND CCAM.ApproverType = 1 AND CCAM.IsActive = 1 AND ISNULL(CCAM.FloorProcess,'') = ''

	IF(@LeadProductId IN (7))
	BEGIN
		IF(@ReasonId IN (90,92,94) AND @AgentTypeID = 1)
    	BEGIN
    	    INSERT INTO #BUDetails(BUEmpCode, BUUserName, BUEmail)
    	    SELECT UD.EmployeeId AS BUEmpCode,UD.UserName AS BUUserName,UD.Email As BUEmail
		    FROM MTX.CreditChangeApproverMapping CCAM
		    INNER JOIN CRM.UserDetails UD ON UD.UserID = CCAM.UserID 
		    WHERE CCAM.AgentType = @AgentTypeID AND CCAM.ProductID = @LeadProductId AND CCAM.ApproverType = 3 AND CCAM.IsActive = 1 AND CCAM.FloorProcess = 'BU'
    	END
		ELSE IF(@ReasonId IN (145,146))
    	BEGIN
    	    INSERT INTO #BUDetails(BUEmpCode, BUUserName, BUEmail)
    	    SELECT UD.EmployeeId AS BUEmpCode,UD.UserName AS BUUserName,UD.Email As BUEmail
		    FROM MTX.CreditChangeApproverMapping CCAM
		    INNER JOIN CRM.UserDetails UD ON UD.UserID = CCAM.UserID 
		    WHERE CCAM.AgentType = @AgentTypeID AND CCAM.ProductID = @LeadProductId AND CCAM.ApproverType = 4 AND CCAM.IsActive = 1 AND CCAM.FloorProcess = 'BU'
    	END
		ELSE IF(@AgentTypeID IN (2,3))
		BEGIN
			INSERT INTO #BUDetails(BUEmpCode, BUUserName, BUEmail)
    	    SELECT UD.EmployeeId AS BUEmpCode,UD.UserName AS BUUserName,UD.Email As BUEmail
		    FROM MTX.CreditChangeApproverMapping CCAM
		    INNER JOIN CRM.UserDetails UD ON UD.UserID = CCAM.UserID 
		    WHERE CCAM.AgentType = @AgentTypeID AND CCAM.ProductID = @LeadProductId AND CCAM.ApproverType = 3 AND CCAM.IsActive = 1 AND CCAM.FloorProcess = 'BU'
		END
	END
	ELSE IF (@LeadProductId = 115)
	BEGIN
		IF(@ReasonId IN (98,100,102,114,116,118,130,132,134))
    	BEGIN
    	    INSERT INTO #BUDetails(BUEmpCode, BUUserName, BUEmail)
    	    SELECT UD.EmployeeId AS BUEmpCode,UD.UserName AS BUUserName,UD.Email As BUEmail
		    FROM MTX.CreditChangeApproverMapping CCAM
		    INNER JOIN CRM.UserDetails UD ON UD.UserID = CCAM.UserID 
		    WHERE CCAM.AgentType = @AgentTypeID AND CCAM.ProductID = @LeadProductId AND CCAM.ApproverType = 3 AND CCAM.IsActive = 1 AND CCAM.FloorProcess = 'BU'
    	END
	END

	SELECT * FROM #BUDetails


    DROP TABLE IF EXISTS #BUDetails;
    DROP TABLE IF EXISTS #TempCommDetails;

END