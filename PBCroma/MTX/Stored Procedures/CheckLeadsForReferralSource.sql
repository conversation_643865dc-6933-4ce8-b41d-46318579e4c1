-- =============================================
-- Author:		System Generated
-- Create date: <Create Date,,>
-- Description:	Check if any of the input lead IDs have leadsource = 'referral'
-- Step 1: Get customerid and productid from crm.leaddetails for input leads
-- Step 2: Check if leadsource is referral for any of those leads then return true else false
-- Usage: 
--   DECLARE @LeadIds MTX.LeadTable;
--   INSERT INTO @LeadIds VALUES (44823706, NULL), (44823707, NULL), (44823708, NULL);
--   EXEC [MTX].[CheckBookingHasReferralsBatch] @LeadIds = @LeadIds;
-- =============================================
-- =============================================
-- Author:		System Generated
-- Create date: <Create Date,,>
-- Description:	Check if any of the input lead IDs have leadsource = 'referral'
-- Step 1: Get customerid and productid from crm.leaddetails for input leads
-- Step 2: Check if leadsource is referral for any of those leads then return true else false
-- Usage: 
--   DECLARE @LeadIds MTX.LeadTable;
--   INSERT INTO @LeadIds VALUES (44823706, NULL), (44823707, NULL), (44823708, NULL);
--   EXEC [MTX].[CheckBookingHasReferralsBatch] @LeadIds = @LeadIds;
-- =============================================
create PROCEDURE [MTX].[CheckLeadsForReferralSource]
(
    @LeadIds [MTX].[LeadTable] READONLY,
    @ProductId SMALLINT = 7
)
AS
BEGIN
    SET NOCOUNT ON;
    SET DEADLOCK_PRIORITY LOW;
    SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

    -- Create temp table and insert customerid along with leadids
    CREATE TABLE #Temp (
        LeadId BIGINT,
        CustomerId BIGINT
    );

    -- Step 1: Insert leadid and customerid from leaddetails for input leads
    INSERT INTO #Temp (LeadId, CustomerId)
    SELECT 
        LI.leadId,
        LD.CustomerID
    FROM @LeadIds LI
    LEFT JOIN [MATRIX].[CRM].[LeadDetails] LD WITH(NOLOCK) ON LD.LeadId = LI.leadId;

    SELECT 
       TP.LeadID
	   from #Temp TP left join [MATRIX].[CRM].[LeadDetails] LD WITH(NOLOCK) on LD.CustomerID = TP.CustomerId where LD.LeadSource = 'referral'
	   and LD.ProductId = @ProductId
   

    -- Clean up temp table
    DROP TABLE #Temp;

END 