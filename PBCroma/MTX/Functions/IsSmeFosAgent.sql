
CREATE FUNCTION [MTX].[IsSmeFosAgent]
(
	@AgentId  BIGINT
)
RETURNS BIT
AS
BEGIN
	DECLARE @IsFos BIT=0;

	IF EXISTS
	(
		SELECT TOP 1 UGRM.GroupId
		FROM CRM.UserGroupRoleMapNew UGRM (NOLOCK)
		INNER JOIN CRM.UserDetails (NOLOCK) UD
		ON UD.UserID=UGRM.UserId AND UD.IsActive=1
		WHERE UGRM.UserId=@AgentId
		AND UGRM.GroupId IN (1617,1618,1619,1897,2048,2057,2684,3466,3667,3668,3669)
	)
	BEGIN
		SET @IsFos=1;
	END

	RETURN @IsFos;
END