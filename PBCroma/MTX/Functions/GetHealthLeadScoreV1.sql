SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO



ALTER function [MTX].[GetHealthLeadScoreV1]
(
	@IsSelfSelect INT,
	@PrevBooking BIT,
	@RepeatCustomer BIT,
	@LeadSource VARCHAR(25),
	@Utm_source VARCHAR(255), 
	@UTM_Medium VARCHAR(255),    	
	@Source VARCHAR(100),
	@UTM_Term VARCHAR(255),
	@Utm_campaign VARCHAR(255),
    @BrandName VARCHAR(255),
    @Country VARCHAR (100),
    @CityID INT,
    @Age INT,
    @CMAge INT,
    @LeadID BIGINT,
	@IsPED BIT,
	@LeadCreatedOn DATETIME,
	@CMCount INT,
    @AdultCount TINYINT,
    @ChildCount TINYINT,
    @ClusterId INT,
    @ProcessName VARCHAR(30)='OB' ,
    @AnnualIncome BIGINT=0 ,
    @PED VARCHAR(50),
    @CustPrevProduct SMALLINT,
    @PayuAffluence INT = -2,
    @PayuPropensity INT = -2
)
RETURNS DECIMAL(10,2)
AS
BEGIN
    DECLARE @LeadScore DECIMAL(10,2)=0.00
    DECLARE @UTMScore DECIMAL(10,2)=0.00
    DECLARE @CustomUTMScore DECIMAL(10,2)=0.00    
    DECLARE @LeadSourceScore DECIMAL(10,2)=0.00    
    DECLARE @RepeatScore DECIMAL(10,2)=0.00
    DECLARE @SelfSelectScore DECIMAL(10,2)=0.00
    DECLARE @BookedScore DECIMAL(10,2)=0.00
    DECLARE @CustSource VARCHAR(50)
    DECLARE @ProductID SMALLINT = 2
    DECLARE @BrandScore DECIMAL(10,2)=0.00
    DECLARE @CityScore DECIMAL(10,2)=0.00
    DECLARE @AgeScore DECIMAL(10,2)=0.00
    DECLARE @CMAgeScore DECIMAL(10,2)=0.00
    DECLARE @PEDScore DECIMAL(10,2)=0.00
    DECLARE @ProfileScore DECIMAL(10,2)=0.00
    DECLARE @ClusterScore DECIMAL(10,2)=0.00
    DECLARE @IsClusterScore BIT=0
    DECLARE @AnnualIncomeScore DECIMAL(10,2)=0.00
    DECLARE @CustPrevProductScore DECIMAL(10,2)=0.00
    DECLARE @PayuAffluenceScore DECIMAL(10,2)=0.00
    DECLARE @PayuPropensityScore DECIMAL(10,2)=0.00
    
	IF @PrevBooking IS NULL 
        SET @PrevBooking=0	
    IF @IsSelfSelect IS NULL 
        SET @IsSelfSelect=0
    IF @RepeatCustomer IS NULL 
        SET @RepeatCustomer=0    
    IF @IsPED IS NULL 
        SET @IsPED=0        
        
	
    IF @IsSelfSelect > 0
        SET @IsSelfSelect=1			
	
	IF @ProcessName='FF' OR @ProcessName=''
		SET @ProcessName='OB'

    IF ISNULL(@Country,'') NOT in ('392','91','999','INDIA','0','NULL','') 
        BEGIN
            -- SET @ProcessName='NRI' 
            SET @CityID=@Country
        END    
    -- ELSE IF @IsPED=1 AND @Utm_source LIKE '%yt_%' --AND @Utm_source LIKE '%DayTrader%'    
    --     SET @ProcessName='HEALTH-TELUGU-INF-PED'         
    -- ELSE IF @Utm_source LIKE '%yt_%'--(@Utm_source LIKE '%yt_%' AND @Utm_source LIKE '%DayTrader%') OR @Utm_source='YT_NonBrand_InfluencerAct1'    
    --     SET @ProcessName='HEALTH-TELUGU-INF'    
    -- ELSE IF @IsPED=1       
    --     SET @ProcessName='HEALTH-PED' 
    -- ELSE IF @CMCount=1   
    --     SET @ProcessName='HEALTH-IND'  
    ELSE     
        BEGIN
            -- SET @ProcessName='OB' 
            IF @LeadID % 2=0
                SET @IsClusterScore=1
        END
    
    


    BEGIN
        --Custom_UTM
		--IF @ProcessName='TRAD'
		--	SELECT @CustSource=@Utm_source		
        IF @ProcessName='RETAINERS'
            BEGIN
                SELECT  @CustSource=
                    CASE	WHEN  @Utm_source LIKE '%CRM_SMS_Retainer_BALIC%' AND @Source IN ('1','2') THEN 'BALIC FJ'
                        WHEN  @Utm_source LIKE '%CRM_SMS_Retainer_BALIC%' AND @UTM_Term ='talktime' THEN 'Talktime C2L'
                        WHEN  @Utm_source LIKE '%CRM_SMS_Retainer_BALIC%' then 'BALIC C2L'						
                        ELSE 'Others' END 
            END
        ELSE IF @ProcessName IN ('NRI','Health-NRI-V2', 'HEALTH-NRI-MALYALAM', 'HEALTH-NRI-MALAYALAM-V2') AND @Utm_Source IN ('google','google_','bing','yahoo') AND ((@UTM_Medium like '%ppc%' OR @UTM_Medium like '%cpc%')  AND ( @UTM_Medium NOT like '%remarketing%' and  @UTM_Medium NOT LIKE '%disp%')) AND (ISNULL(@Utm_Term,'')='')
            BEGIN
                SELECT @CustSource= 'NBS_permax' 
            END
        ELSE
            BEGIN
                SELECT  @CustSource=[MTX].[GetCustomUTM](@UTM_Source,@UTM_Medium,@LeadSource,@UTM_Term,@Utm_campaign,'',2)
            END 

        -- PED Score
        IF @ProcessName='HEALTH-PED-V2'
            BEGIN
                SELECT TOP 1 @PEDScore=ISNULL(Score,0) FROM MTX.PEDScore(NOLOCK) WHERE PED=@PED AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY Id DESC 
                IF ISNULL(@PEDScore,0)=0
                    BEGIN
                        SELECT TOP 1 @PEDScore=ISNULL(Score,0) FROM MTX.PEDScore(NOLOCK) WHERE PED=0 AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY Id DESC
                    END

                SET @ProcessName='HEALTH-PED'
            END
        ELSE 
            -- PED Score -NRI
            SELECT TOP 1 @PEDScore=ISNULL(Score,0) FROM MTX.ScoreMaster(NOLOCK) WHERE ScoreType='IsPED' AND HasValue=@IsPED AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY Id DESC
        
		SELECT TOP 1 @SelfSelectScore=ISNULL(Score,1000) FROM MTX.ScoreMaster(NOLOCK) WHERE ScoreType='IsSelfSelect' AND HasValue=@IsSelfSelect AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY Id DESC 
        
		IF @ProcessName='Health-NRI-V2'
			BEGIN
				SET @ProcessName = 'NRI';
			END
        		        
        --UTM source Score     
        SELECT TOP 1 @CustomUTMScore=ISNULL(Score,0) FROM MTX.CustomUtmScore(NOLOCK) WHERE CustomUtm=@CustSource AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY Id DESC        

        IF ISNULL(@CustomUTMScore,0) =0
        BEGIN
            SELECT TOP 1 @CustomUTMScore=ISNULL(Score,1005) FROM MTX.CustomUtmScore(NOLOCK) WHERE CustomUtm='0' AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY Id DESC        
        END


        

        --Leadsource
        SELECT TOP 1 @LeadSourceScore=ISNULL(Score,0) FROM MTX.LeadSourceScore(NOLOCK) WHERE LeadSource=@LeadSource AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY Id DESC      
        IF ISNULL(@LeadSourceScore,0) =0
        BEGIN    
            SELECT TOP 1 @LeadSourceScore=ISNULL(Score,1000) FROM MTX.LeadSourceScore(NOLOCK) WHERE LeadSource='0' AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY Id DESC      
        END

        --IsRepeat
        SELECT TOP 1 @RepeatScore=ISNULL(Score,0) FROM MTX.ScoreMaster(NOLOCK) WHERE ScoreType='IsRepeat' AND HasValue=@RepeatCustomer AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY Id DESC 

        --Self SELECT             
		--SELECT TOP 1 @SelfSelectScore=ISNULL(Score,1000) FROM MTX.ScoreMaster(NOLOCK) WHERE ScoreType='IsSelfSelect' AND HasValue=@IsSelfSelect AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY Id DESC 		

        -- Previous Booking
        
        SELECT TOP 1 @BookedScore=ISNULL(Score,0) FROM MTX.ScoreMaster(NOLOCK) WHERE ScoreType='IsBooked' AND HasValue=@PrevBooking AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY Id DESC 

        --Profile Score -- NRI
        SELECT TOP 1 @ProfileScore=ISNULL(Score,0) FROM MTX.AdultChildScore(NOLOCK) WHERE ProductID=@ProductID AND ProcessName=@ProcessName AND AdultCount=ISNULL(@AdultCount,0) AND ChildCount=ISNULL(@ChildCount,0) AND IsActive=1 ORDER BY Id DESC 
        IF ISNULL(@ProfileScore,0) =0
            BEGIN
                SELECT TOP 1 @ProfileScore=ISNULL(Score,0) FROM MTX.AdultChildScore(NOLOCK) WHERE ProductID=@ProductID AND ProcessName=@ProcessName AND AdultCount=0 AND ChildCount=0 AND IsActive=1 ORDER BY Id DESC 
            END
        --City / Country Score       
        SELECT TOP 1 @CityScore=ISNULL(Score,0) FROM MTX.CityScore(NOLOCK) WHERE ProductID=@ProductID AND ProcessName=@ProcessName AND CityID=@CityID AND IsActive=1 ORDER BY Id DESC 
        IF ISNULL(@CityScore,0) =0
            BEGIN
                SELECT TOP 1 @CityScore=ISNULL(Score,0) FROM MTX.CityScore(NOLOCK) WHERE ProductID=@ProductID AND ProcessName=@ProcessName AND CityID=0 AND IsActive=1 ORDER BY Id DESC 
            END
                                            
        --Age SCORE
        SELECT TOP 1 @CMAgeScore=CASE WHEN @ProcessName='NRI' THEN ISNULL(Score,0) ELSE ISNULL(Score,215) END FROM MTX.CMAgeBucketScoreMaster(NOLOCK) WHERE AgeGroup <= @CMAge AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY AgeGroup DESC

        --AnnualIncome
        SELECT TOP 1 @AnnualIncomeScore=ISNULL(Score,0) FROM MTX.AnnualIncomeScore(NOLOCK) WHERE ISNULL(AnnualIncome,0)=ISNULL(@AnnualIncome,0) AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY Id DESC  

        --CustPrevProduct
        SELECT TOP 1 @CustPrevProductScore=ISNULL(Score,0) FROM MTX.CustPrevProductScore(NOLOCK) WHERE ISNULL(PrevProductId,0)=ISNULL(@CustPrevProduct,0) AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY Id DESC
                     
        --PayuAffluenceScore
        SELECT TOP 1 @PayuAffluenceScore=ISNULL(Score,0) FROM MTX.PayuAffluenceScore(NOLOCK) WHERE PayuAffluence=ISNULL(@PayuAffluence,0) AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY Id DESC  

        --PayuPropensityScore
        SELECT TOP 1 @PayuPropensityScore=ISNULL(Score,0) FROM MTX.PayuPropensityScore(NOLOCK) WHERE PayuPropensity=ISNULL(@PayuPropensity,0) AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 ORDER BY Id DESC  

    
        /*
        IF @IsClusterScore=1
            BEGIN
                SELECT TOP 1 @ClusterScore=Score  FROM [MTX].[ClusterScore](NOLOCK) WHERE ClusterID = @ClusterId AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 
                IF  @ClusterScore IS NULL   
                    SELECT TOP 1 @ClusterScore=Score  FROM [MTX].[ClusterScore](NOLOCK) WHERE ClusterID = 0 AND ProductID=@ProductID AND ProcessName=@ProcessName AND IsActive=1 
            END    
        */    


        SET @LeadScore= @CustomUTMScore + @LeadSourceScore  + @RepeatScore + @SelfSelectScore + @BookedScore + @CityScore + @CMAgeScore + @PEDScore + @ProfileScore + @ClusterScore + @AnnualIncomeScore + @CustPrevProductScore + @PayuAffluenceScore + @PayuPropensityScore


    END
        
    RETURN @LeadScore
END
GO
