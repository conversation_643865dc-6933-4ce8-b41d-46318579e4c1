
CREATE FUNCTION [MTX].[GetLeadRank_INV_Allocation]
(
	@CityID INT,
	@InsurerId INT=0,
	@Prebooking BIT,
	@RepeatCust BIT,
	@Premium INT,
	@AnnualIncome BIGINT,
	@SourcePage VARCHAR(100),
	@Age TINYINT,
	@InvType TINYINT,
	@UtmSource VARCHAR(255),
	@UtmMedium VARCHAR(255),
	@LeadSource VARCHAR(25),
	@PayTerm  TINYINT=0,
	@Country VARCHAR(50),
	@UtmTerm VARCHAR(255),
	@Source  VARCHAR(50),
	@Brandname  VARCHAR(50),
	@PlanFType VARCHAR(20),
	@Utmcampaign VARCHAR(255),
	@IsImmediatePension BIT,
    @SeachPlanID INT,
    @StateID INT,
    @UtmContent VARCHAR(255),
    @Name VARCHAR (50)
)
RETURNS SMALLINT
AS
BEGIN

 DECLARE @LeadRank SMALLINT = 0
 DECLARE @IsNRI BIT=0
 DECLARE @Custom_UTM AS VARCHAR(50)
 DECLARE @Custom_UTM1 AS VARCHAR(50)

	IF ISNULL(@Country,'') NOT in ('392','91','999','INDIA','0','NULL','') 
		SET @IsNRI=1

    
    SELECT  @Custom_UTM1=[MTX].[GetCustomUTM](@UtmSource,@UtmMedium,@LeadSource,'','','',115)
			    

    SELECT @Custom_UTM=  CASE  WHEN @UtmSource like '%CRM%' THEN 'CRM'
									WHEN @UtmSource IN ('colombia','outbrain','taboola','facebook') OR (@UtmSource IN ('google') and ( @UtmMedium like '%remarketing%' or  @UtmMedium like '%disp%')) THEN 'Non Brand Display'
									Else 'Other' End 
 IF @LeadSource='referral' AND @UtmSource='FOS_referral'
        SET @LeadRank = 0        
 ELSE IF @UtmSource = 'Pension_Upsell' AND @LeadSource='Reopen' AND @Utmcampaign='Upsell_Pension_on_Non-Issued_New_Inv_Cust'
        SET @LeadRank = 299 
 ELSE IF @IsNRI=1 AND @Country='2'
        SET @LeadRank = 0       
 ELSE IF @LeadSource IN ('Crosssell','Referral') AND @UtmSource IN ('Health','Health_RM','health_claim') AND ISNULL(@StateID,0) NOT IN (1,37,34,16,17,30) 
        SET @LeadRank = 208
 ELSE IF @IsNRI=0 AND (@UtmMedium like '%000365%' OR @UtmMedium like '%000366%')
        SET @LeadRank = 55                  
--  ELSE IF @IsNRI=1 AND @Custom_UTM1 like '%direct%' AND @Country='92'
--         SET @LeadRank = 231                  
 ELSE IF @IsNRI=1 AND @Name LIKE '%Valued%Customer%'       
        SET @LeadRank = 90
 ELSE IF @UtmSource = 'Inv_NRI_Cust_Retarget' AND @LeadSource='Reopen' 
        SET @LeadRank = 36               
 ELSE IF @IsNRI=1 AND @Utmcampaign LIKE '%Malayalam%'
        SET @LeadRank = 87
 ELSE IF @IsNRI=1 AND @Utmcampaign LIKE '%IV0747_115on115_book_res_nfo_pnb_nri_english_exp_%' AND @Country IN ('24','25','218','254','255','256','313','180','72','36','47','55','154','164','180','217','218','230','243','280','328','355','384') 
        SET @LeadRank = 71
 ELSE IF @IsNRI=1 AND @Utmcampaign LIKE '%IV0747_115on115_book_res_nfo_pnb_nri_english_exp_%' AND @Country IN ('35','187','271','272','288','308','246')  
        SET @LeadRank = 31
 ELSE IF @IsNRI=1 AND @Utmcampaign LIKE '%IV0747_115on115_book_res_nfo_pnb_nri_english_exp_%' AND @Country IN ('375','393')  
        SET @LeadRank = 77
 ELSE IF @IsNRI=1 AND @Utmcampaign LIKE '%IV0747_115on115_book_res_nfo_pnb_nri_english_exp_%' AND @Country IN ('12','44','63','144','231','378')  
        SET @LeadRank = 74
 ELSE IF @IsNRI=1 AND @Utmcampaign LIKE '%IV0747_115on115_book_res_nfo_pnb_nri_english_exp_%' AND @Country IN ('3','4','5','10','26','27','28','29','30','31','32','33','38','39','40','41','42','43','49','50','56','57','81','82','87','88','89','92','93','94','95','101','113','114','115','116','117','118','119','120','121','127','128','129','130','131','132','133','134','135','136','140','141','142','143','155','156','157','158','159','160','161','167','168','169','170','171','173','174','176','195','203','204','205','207','208','209','234','247','248','249','250','251','252','266','267','268','269','270','281','282','283','284','285','289','290','291','292','294','295','296','297','298','324','325','326','327','340','341','342','343','344','345','346','347','348','370','371','372','373','374','376')  
        SET @LeadRank = 81
 ELSE IF @Country in ('340','341') AND @Custom_UTM1 like '%App%Non%Organic%'  AND @Utmcampaign = 'policybazaar_in' 
        SeT @Leadrank = 231       
 ELSE IF @IsNRI=1 AND @LeadSource='reopen' AND @UtmSource='CrossSell_Transfer_to_Pension' AND @Utmcampaign LIKE 'Term_CrossSell_Transfer_LR%'
        SET @LeadRank = 300
 ELSE IF @LeadSource='reopen' AND @UtmSource='CrossSell_Transfer_to_Pension' AND @Utmcampaign LIKE 'Term_CrossSell_Transfer_LR%'
        SET @LeadRank = 290       
 /*ELSE IF @LeadSource='reopen' AND @UtmSource='reject_reopen' AND @Utmcampaign='Rejected_Leads_for_Appointment_Gen'
        SET @LeadRank = 40*/
 ELSE IF @UtmSource LIKE '%crm%' AND @Utmcampaign LIKE '%bajaj_pasa%'
        SET @LeadRank = 47            
 ELSE IF @IsNRI=0 AND @UtmSource='PaisaCampaignSMS' 
		SET @LeadRank = 67
 ELSE IF @IsNRI=0 AND @StateID IN (35,36) AND (@Utmcampaign LIKE '%Investment_Domestic_Retirement_Delhi_NCR_Broad_Above_45%' OR @Utmcampaign LIKE '%Investment_Domestic_Retirement_Delhi_NCR_Exact_Above_45%' OR @Utmcampaign LIKE '%IV0334_115on7_rej_res_ret_pension_english%' OR @Utmcampaign LIKE '%growth_app_cmp_nfo_hdfc_pen%' OR @Utmcampaign LIKE '%Investment_Domestic_Retirement_Rest_of_India_Broad_Age_Above_4500Fixed_Deposit%' OR @Utmcampaign LIKE '%Investment_Domestic_Retirement_Rest_of_India_Broad_Age_Above_4500NPS%' OR @Utmcampaign LIKE '%Investment_Domestic_Retirement_Delhi_NCR_Broad_Above_4500NPS%')
		SET @LeadRank = 295
 ELSE IF @IsNRI=0 AND (@Utmcampaign LIKE '%Investment_Domestic_Retirement_Delhi_NCR_Broad_Above_45%' OR @Utmcampaign LIKE '%Investment_Domestic_Retirement_Delhi_NCR_Exact_Above_45%' OR @Utmcampaign LIKE '%Investment_Domestic_Retirement_Rest_of_India_Broad_Age_Above_4500Fixed_Deposit%' OR @Utmcampaign LIKE '%Investment_Domestic_Retirement_Rest_of_India_Broad_Age_Above_4500NPS%' OR @Utmcampaign LIKE '%Investment_Domestic_Retirement_Delhi_NCR_Broad_Above_4500NPS%' OR @Utmcampaign LIKE '%IV0533_115on115_rej_res_nfo_tata_pension_english_210125%' OR @Utmcampaign LIKE '%Investment_Domestic_Retirement_Delhi_NCR_Broad_Above_4500Mutual_Funds%' OR @Utmcampaign LIKE '%Investment_Domestic_Retirement_Delhi_NCR_Exact_Above_4500Mutual_Funds%' OR @Utmcampaign LIKE '%Investment_Domestic_Retirement_Rest_of_India_Broad_Above_4500SIP_Calculator%' OR @Utmcampaign LIKE '%Investment_Domestic_Retirement_Rest_of_India_Broad_Age_Above_4500Mutual_Funds%' OR @Utmcampaign LIKE '%Investment_Domestic_Retirement_Rest_of_India_Exact_Age_Above_4500Mutual_Funds%' OR @Utmcampaign LIKE '%Investment_Domestic_Retirement_Rest_of_India_Broad_Age_Above_4500Annuity%')
		SET @LeadRank = 272    
 ELSE IF @IsNRI=0 AND (@Utmcampaign LIKE 'Investment_Domestic_Pension_APRO-POLICYBA-ULIPtravelandCreditScore' OR @Utmcampaign LIKE 'Investment_Domestic_Pension_APRO-POLICYBA-ULIPPremiumCCIncome' OR @Utmcampaign LIKE 'Investment_Domestic_Pension_APRO-POLICYBA-SavingsInvestPremiumCCInc' OR @Utmcampaign LIKE 'Investment_Domestic_Pension_APRO-POLICYBA-ULIPPremiumCCIncome' OR @Utmcampaign LIKE 'Investment_Domestic_Pension_APRO-POLICYBA-ULIPPrem')        
        SET @LeadRank = 271
 ELSE IF @IsNRI=0 AND (@UtmMedium like ('%000203%') OR @UtmMedium LIKE ('%000204%') OR @UtmMedium LIKE ('%growth_app_cmp_nfo_pra_pen_1224%')) 
        SET @LeadRank = 272
 ELSE IF @IsNRI=0 AND @Custom_UTM1='FB Display' AND @Utmcampaign LIKE '%nvestment_Domestic_Retirement_Above_40%' 
 				  AND ((@PlanFType = 't') or (ISNULL(@PlanFType,'') <> 't' and @InvType = '2'))     
				  --AND @Age > =40 
        	SET @LeadRank = 271
 ELSE IF @IsNRI=0 AND (@UtmMedium='growth_app_cmp_annuityparent' OR @UtmMedium LIKE '%000008%' OR @UtmMedium LIKE '%000145%' OR @UtmMedium LIKE '%000009%' OR @UtmMedium = 'growth_app_cmp_annuityparent' OR @Utmcampaign = 'Investment_Domestic_Retirement_Planning' 
                            OR @UtmMedium = 'growth_app_cmp_nfo_hdfc_pen'
                            OR @UtmMedium LIKE '%000162%'
                            OR @UtmMedium LIKE '%000145%'
                            OR @UtmMedium LIKE '%growth_app_cmp_nfo_hdfc_pen%'
                            OR @UtmMedium LIKE '%000171%'
                            OR @UtmMedium LIKE '%000172%'
                            OR @UtmMedium LIKE '%growth_app_cmp_nfo_hdfc_pen_1124%'
                            OR @Utmcampaign LIKE '%Inv_Dom_Pension_Above_45%'
                            OR @UtmMedium LIKE '%000237%' OR @UtmMedium LIKE '%000238%' OR @UtmMedium LIKE '%growth_app_cmp_nfo_tata_pen_0125%'
                            OR @UtmMedium LIKE ('%growth_app_cmp_pnfo_2502_1%') OR  @UtmMedium LIKE ('%growth_app_cmp_pnfo_2502_2%')
                            OR @UtmMedium LIKE ('%growth_app_cmp_pnfo_2503_1%') OR @UtmMedium LIKE ('%growth_app_cmp_pnfo_2503_2%')
                            OR @UtmMedium LIKE ('%000273%') OR @UtmMedium LIKE ('%000282%')
                            OR @UtmMedium LIKE ('%000295%') 
                            OR @UtmMedium LIKE ('%000296%') 
                            OR @UtmMedium LIKE ('%000297%') 
                            OR @UtmMedium LIKE ('%000298%') 
                            OR @UtmMedium LIKE ('%000299%') 
                            OR @UtmMedium LIKE ('%000300%') 
                            OR @UtmMedium LIKE ('%000301%') 
                            OR @UtmMedium LIKE ('%000302%') 
                            OR (@UtmMedium IS NOT NULL AND LEN(@UtmMedium) > 18 AND SUBSTRING(@UtmMedium, 18, LEN(@UtmMedium)) LIKE '%_90000%')
                            OR @UtmMedium LIKE ('%growth_app_cmp_pnfo_2504_1%') 
                            OR @UtmMedium LIKE ('%growth_app_cmp_pnfo_2504_2%') 
                            OR @UtmMedium LIKE ('%growth_app_cmp_pnfo_2505_1%') 
                            OR @UtmMedium LIKE ('%growth_app_cmp_pnfo_2505_2%') 
                            OR @UtmMedium LIKE ('%growth_app_cmp_pnfo_2506_1%') 
                            OR @UtmMedium LIKE ('%growth_app_cmp_pnfo_2506_2%')
                            OR @UtmMedium LIKE '%000332%'
                            OR @UtmMedium LIKE '%000333%'
                            OR @UtmSource LIKE '%bureau_crosssell_policy_pension%'
                      )				  
        	SET @LeadRank = 271           
 ELSE IF @IsNRI=0 AND (@Utmcampaign LIKE '%IV0153_115on7_book_res_Age_pension_english%' OR @Utmcampaign LIKE '%IV0154_115on2_book_res_Age_pension_english%' OR @Utmcampaign IN ('Investment_Domestic_Retirement_Planning') OR @Utmcampaign LIKE '%IV0332_115on7_book_res_ret_pension_english%' OR @Utmcampaign LIKE '%IV0333_115on2_book_res_ret_pension_english%'  OR @Utmcampaign LIKE '%IV0699_115on7_book_res_ret_new_pension_english_sf_%'  OR @Utmcampaign LIKE '%IV0700_115on2_book_res_ret_pension_english_sf_%')           
            SET @LeadRank = 274
 ELSE IF @IsNRI=0 AND @UtmMedium = 'growth_app_is_annuity' AND @Age >= 40
            SET @LeadRank = 271
 ELSE IF @IsNRI=0 AND @Utmcampaign LIKE '%Inv_Dom_Pension_Above_40%' 
            SET @LeadRank = 271            
 ELSE IF @IsNRI=0 AND ( @Utmcampaign  LIKE '%Investment_Domestic_YT_Retirement%' OR
                        @Utmcampaign like '%IV0334_115on7_rej_res_ret_pension_english%' OR 
                        @Utmcampaign like '%IV0335_115on115_rej_res_ret_pension_english%' OR
                        @Utmcampaign like '%IV0336_115on115_rej_MT90_ret_pension_english%' OR
                        @UtmMedium LIKE ('%000213%') OR @UtmMedium LIKE ('%000214%') OR @UtmMedium LIKE '%growth_app_cmp_nfo_bn_pen_0125%'
                        OR @UtmMedium LIKE ('%000249%') OR  
                        @UtmMedium LIKE ('%000250%') OR  
                        @UtmMedium LIKE ('%000251%') OR  
                        @UtmMedium LIKE ('%000252%') OR  
                        @UtmMedium LIKE ('%000253%') OR  
                        @UtmMedium LIKE ('%000254%') OR  
                        @UtmMedium LIKE ('%000255%') OR  
                        @UtmMedium LIKE ('%000256%') OR  
                        @UtmMedium LIKE ('%growth_app_cmp_pnfo_1_0225%') OR  
                        @UtmMedium LIKE ('%growth_app_cmp_pnfo_2_0225%') OR  
                        @UtmMedium LIKE ('%growth_app_cmp_pnfo_1_0325%') OR  
                        @UtmMedium LIKE ('%growth_app_cmp_pnfo_2_0325%') OR
                        @UtmMedium LIKE ('%000249%') OR  @UtmMedium LIKE ('%000250%') OR @UtmMedium LIKE ('%growth_app_cmp_pnfo_1_0225%')
                      )  
            SET @LeadRank = 271  
 ELSE IF @IsNRI=0 AND (@Utmcampaign LIKE '%IV0153_115on7_book_res_Age_pension_english_%' OR @Utmcampaign LIKE '%IV0154_115on2_book_res_Age_pension_english_%')
            SET @LeadRank = 274                     
 ELSE IF @IsNRI=0 AND @Custom_UTM1='Non Brand Search' AND @Utmcampaign LIKE '%broad%age%above%45%' 
 				  AND ((@PlanFType = 't') or (ISNULL(@PlanFType,'') <> 't' and @InvType = '2'))     
				  AND @Age > =40 
        	SET @LeadRank = 271 
 ELSE IF @IsNRI=0 AND @LeadSource='pbmobileapp' AND (@UtmMedium LIKE '%000095%' OR @UtmMedium LIKE '%000103%'  OR @UtmMedium LIKE '%Icici_pension_nfo%')
        	SET @LeadRank = 271 

 ELSE IF @IsNRI=0 AND @Age >=22 AND (
       @UtmSource LIKE '%yt_brand%jun23%' OR @UtmContent = 'home_carousel_trad' OR @UtmContent='home_taxsaving23'
    OR (@LeadSource = 'pbmobileapp' AND @UtmMedium LIKE '%growth_sel%inv_40%')
    OR (@LeadSource = 'pbmobileapp' AND @UtmMedium LIKE 'push_crmsegment_promo%inv%hni%')
    OR (@UtmSource = 'whatsapp_crm_sales' AND @Utmcampaign LIKE '%hni%')
    OR (@UtmTerm LIKE '%santosh%')
 )
        SET @LeadRank = 225 -- score based (225,226,227,228)      
 ELSE IF @UtmSource LIKE '%tata%consumer%portal%' 
    BEGIN
        IF @Country IN ('35','187','271','272','288','308','246','375','184', '185', '321')  --GCC   
            SET @LeadRank = 31       
        ELSE IF @Country IN ('36','47','55','154','164','180','217','218','230','243','280','328','355','384','24','254','313','25','255','256','72')  --Hungry Hunters ANZ	    
            SET @LeadRank = 71
        ELSE IF @Country IN ('12','44','63','144','231','378','360','179') --US/CANADA
			SET @LeadRank = 74 --SCORE BASED
		ELSE IF @Country IN ('3','4','5','10','26','27','28','29','30','31','32','33','38','39','40','41','42','43','49','50','56','57','81','82','87','88','89','92','93','94','95','101','113','114','115','116','117','118','119','120','121','127','128','129','130','131','132','133','134','135','136','140','141','142','143','155','156','157','158','159','160','161','167','168','169','170','171','173','174','176','195','203','204','205','207','208','209','234','247','248','249','250','251','252','266','267','268','269','270','281','282','283','284','285','289','290','291','292','294','295','296','297','298','324','325','326','327','340','341','342','343','344','345','346','347','348','370','371','372','373','374','376','390','262','172') --europe
			SET @LeadRank = 81 -- SCORE BASED    
        ELSE
            SET @LeadRank = 56        
    END    
 /*ELSE IF @Utmcampaign LIKE '%ULIP_South_Mumbai%'     
        SET @LeadRank = 49*/
 ELSE IF @Utmcampaign LIKE '%Investment_Domestic_PayU_Ultra_HNI%'     
        SET @LeadRank = 270            
 /*      stopped because it will go into 105-108 or 115-118         
 ELSE IF @InvType = 1 AND @PlanFType='T' AND @stateid=20 AND @CityID IN (302,316,666)
		SET @LeadRank = 97 -- Appointment generation*/
 /*ELSE IF @PlanFType='T' AND @stateid='20' AND ISNULL(@CityID,0) NOT IN (309,316,666)
		SET @LeadRank = 98   -- Maharashtra_Trad_Offline */   
 ELSE IF      @LeadSource LIKE '%PB%' AND  @Custom_UTM1='SEO' AND @SeachPlanID IN (15757,32684,15768) AND @InsurerId=0 AND @IsNRI=0 
        SET @LeadRank = 19
 ELSE IF @InvType=1 AND ISNULL(@StateID,0) IN (35,36) AND (@InsurerId=10 OR (@SeachPlanID IN (15757,32684) AND @InsurerId > 0) OR (@SeachPlanID IN (15768))) -- AND ISNULL(@PlanFType,'') <> 'T' 
                SET @LeadRank = 27 -- LIC-DELHI-FOS  
 ELSE IF @InvType=1 AND ISNULL(@PlanFType,'') <> 'T' AND ISNULL(@StateID,0) IN (35,36) AND @InsurerId=4                                               
                SET @LeadRank = 29 -- Birla-Delhi-FOS  	               	       
 ELSE IF  @IsNRI=0 AND (@SeachPlanID IN (20035) OR @InsurerId=14) 
        SET @LeadRank = 26 -- SBI             
 ELSE IF @IsNRI=0 AND @UtmSource LIKE '%whatsapp%crm%sales%' AND @Utmcampaign LIKE '%IV0220%115on7%rej%res%ret%pension%english_%'
		SET @LeadRank = 271
 ELSE IF @IsNRI=0 AND @Utmcampaign LIKE '%Investment_Domestic_Retirement_HNI%'
		SET @LeadRank = 271       
 ELSE IF @IsNRI=0 AND @UtmSource LIKE '%whatsapp_crm_sales%' AND @Utmcampaign LIKE '%pension%'
		SET @LeadRank = 271
 ELSE IF @IsNRI=0 AND @Utmcampaign = 'Investment_Domestic_Retirement_Rest_of_India_Broad_Age_Above_45' AND @Age >=30
		SET @LeadRank = 271          
 ELSE IF @IsNRI=0 AND @Utmcampaign = 'IV0150_115on115_rej_res_Return_pension_english_' OR  @Utmcampaign LIKE '%IV0074_115on115_rej_res_ret_pension_english_%'
		SET @LeadRank = 271                 
 ELSE IF  (@UtmSource LIKE '%crm%' AND @Utmcampaign LIKE '%tata_pasa%') OR (@LeadSource = 'pbmobileapp' AND @UtmMedium LIKE '%tata_pasa%')      
    BEGIN
        IF @Country IN ('35','187','271','272','288','308','246','375','184', '185', '321')  --GCC   
            SET @LeadRank = 31        
        ELSE IF @Country IN ('36','47','55','154','164','180','217','218','230','243','280','328','355','384','24','254','313','25','255','256','72')  --Hungry Hunters ANZ	    
            SET @LeadRank = 71 
        ELSE IF @Country IN ('12','44','63','144','231','378','360','179') --US/CANADA
			SET @LeadRank = 74 --SCORE BASED
		ELSE IF @Country IN ('3','4','5','10','26','27','28','29','30','31','32','33','38','39','40','41','42','43','49','50','56','57','81','82','87','88','89','92','93','94','95','101','113','114','115','116','117','118','119','120','121','127','128','129','130','131','132','133','134','135','136','140','141','142','143','155','156','157','158','159','160','161','167','168','169','170','171','173','174','176','195','203','204','205','207','208','209','234','247','248','249','250','251','252','266','267','268','269','270','281','282','283','284','285','289','290','291','292','294','295','296','297','298','324','325','326','327','340','341','342','343','344','345','346','347','348','370','371','372','373','374','376','390','262','172') --europe
			SET @LeadRank = 81 -- SCORE BASED           
        ELSE
            SET @LeadRank = 55
    END        
 ELSE IF @IsNRI =1 AND @UtmSource = 'NRI_UPSELL' --AND @Country IN ('24','25','218','254','255','256','313','180','72','36','47','55','154','164','180','217','218','230','243','280','328','355','384') 
        SET @LeadRank = 39
 ELSE IF @IsNRI =1 AND @UtmSource = 'NRI_AI_UPSELL_FOS' --AND @Country IN ('24','25','218','254','255','256','313','180','72','36','47','55','154','164','180','217','218','230','243','280','328','355','384') 
        SET @LeadRank = 307        
 /*ELSE IF @IsNRI =1 AND @UtmSource = 'NRI_UPSELL' 
        SET @LeadRank = 36*/     
 
 /*ELSE IF @IsNRI =0 AND (@UtmSource = 'AI_Upsell' OR @UtmSource = 'AI_Upsell_FOS' OR @UtmSource = 'AI_Upsell_FOS_HNI' 
                            OR @UtmSource = 'AI_Upsell_FOS_APE') AND @CityID in (6,837,853,571,845,18,843,854) 
        SET @LeadRank = 149*/                    
 ELSE IF @IsNRI =1 AND @UtmSource = 'AI_UPSELL' 
        SET @LeadRank = 37 
 /*ELSE IF @UtmSource = 'AI_Upsell_FOS_Birla' 
        SET @LeadRank = 130*/        
 /*ELSE IF @UtmSource = 'AI_UPSELL' AND @Utmcampaign='HDFC_PASA_Proposition'
        SET @LeadRank = 47     */           
 /*ELSE IF @UtmSource IN ('AI_Upsell','AI_Upsell_FOS','AI_Upsell_FOS_HNI','AI_Upsell_FOS_APE') AND (@StateID=30 OR @CityID in (420)) 
        SET @Leadrank = 129*/
 ELSE IF @UtmSource = 'AI_UPSELL' AND @Age >= 45
		SET @LeadRank = 274      
 ELSE IF @UtmSource = 'AI_UPSELL' AND @CityID IN (551,555) 
		SET @LeadRank = 280
 ELSE IF @UtmSource = 'AI_UPSELL' 
		SET @LeadRank = 5
 ELSE IF @UtmSource = 'AI_UPSELL_FOS' AND @Age >= 40
		SET @LeadRank = 274 
 ELSE IF @UtmSource = 'AI_Upsell_FOS_HNI' AND @UtmMedium='INFORCE' AND @Age >=45
		SET @LeadRank = 335
 ELSE IF @UtmSource = 'AI_Upsell_FOS_HNI'  AND @Age >=45 
		SET @LeadRank = 336                     
 ELSE IF @UtmSource = 'AI_Upsell_FOS_APE' AND @UtmMedium='INFORCE' AND @Age >=45 
		SET @LeadRank = 331
 ELSE IF @UtmSource = 'AI_Upsell_FOS_APE' AND @Age >=45  
		SET @LeadRank = 332              
 ELSE IF @UtmSource IN ('AI_Upsell_FOS_HNI','AI_Upsell_FOS_APE','AI_UPSELL_FOS') AND @CityID IN (551,555)
		SET @LeadRank = 280  
 ELSE IF @UtmSource = 'AI_Upsell_FOS_HNI' AND @UtmMedium='INFORCE' 
		SET @LeadRank = 325
 ELSE IF @UtmSource = 'AI_Upsell_FOS_HNI' 
		SET @LeadRank = 326                     
 ELSE IF @UtmSource = 'AI_Upsell_FOS_APE' AND @UtmMedium='INFORCE' 
		SET @LeadRank = 321
 ELSE IF @UtmSource = 'AI_Upsell_FOS_APE' 
		SET @LeadRank = 322                     
 ELSE IF @UtmSource = 'Child_Niveshkar_Befikar' 
		SET @LeadRank = 21       
 /*ELSE IF @UtmSource='whatsapp_crm_sales' AND ISNULL(@Utmcampaign,'') like '%whatsapp%growth%'	
		SET @LeadRank = 30*/ 
 ELSE IF @UtmSource = 'AI_UPSELL_FOS' AND @UtmMedium='INFORCE'
		SET @LeadRank = 8
 ELSE IF @UtmSource = 'AI_UPSELL_FOS' 
		SET @LeadRank = 9       
 /*ELSE IF @Utmcampaign LIKE '%Tataaia_sat%' 
		SET @LeadRank = 96*/
--  ELSE IF @Utmcampaign LIKE '%Pnb_sat%' 
-- 		SET @LeadRank = 97
--  ELSE IF @Utmcampaign LIKE '%Bajaj_sat%' 
-- 		SET @LeadRank = 98
 ELSE IF  @IsNRI=1 AND @Utmcampaign LIKE '%Vernacular%' AND @Utmcampaign NOT LIKE '%Hindi%'
		SET @LeadRank = 34
 /*   discontinued on the request of Keshav on 24 july 2023 , will be included in lead ranking 
restrted again on 14 aug 2023
 */
 ELSE IF  @IsNRI=1 AND (@UtmSource='whatsapp_crm_sales') AND ISNULL(@Source,'0') = '0' AND @Country IN ('35','187','271','272','288','308','246','375') -- GCC and UAE
	BEGIN
        SET @LeadRank = 252
    END
 ELSE IF  @IsNRI=1 AND (@UtmSource='whatsapp_crm_sales') AND ISNULL(@Source,'0') = '0' AND @Country IN ('3','4','5','10','26','27','28','29','30','31','32','33','38','39','40','41','42','43','49','50','56','57','81','82','87','88','89','92','93','94','95','101','113','114','115','116','117','118','119','120','121','127','128','129','130','131','132','133','134','135','136','140','141','142','143','155','156','157','158','159','160','161','167','168','169','170','171','173','174','176','195','203','204','205','207','208','209','234','247','248','249','250','251','252','266','267','268','269','270','281','282','283','284','285','289','290','291','292','294','295','296','297','298','324','325','326','327','340','341','342','343','344','345','346','347','348','370','371','372','373','374','376') -- EUrope
	BEGIN
        SET @LeadRank = 253
    END
 ELSE IF  @IsNRI=1 AND (@UtmSource='whatsapp_crm_sales') AND ISNULL(@Source,'0') = '0' AND @Country IN ('24','25','218','254','255','256','313','180','72','36','47','55','154','164','180','217','218','230','243','280','328','355','384') -- ANZ
	BEGIN
        SET @LeadRank = 251
    END       
 ELSE IF  @IsNRI=1 AND (@UtmSource='whatsapp_crm_sales') AND ISNULL(@Source,'0') = '0' AND @Country IN ('12','44','63','144','231','378') -- US
	BEGIN
        SET @LeadRank = 254
    END          
 ELSE IF  @IsNRI=1 AND (@UtmSource = 'crmmail' OR @UtmSource='whatsapp_crm_sales') AND ISNULL(@InsurerId,0)=0
	BEGIN
		IF @Country IN ('3','4','5','10','26','27','28','29','30','31','32','33','38','39','40','41','42','43','49','50','56','57','81','82','87','88','89','92','93','94','95','101','113','114','115','116','117','118','119','120','121','127','128','129','130','131','132','133','134','135','136','140','141','142','143','155','156','157','158','159','160','161','167','168','169','170','171','173','174','176','195','203','204','205','207','208','209','234','247','248','249','250','251','252','266','267','268','269','270','281','282','283','284','285','289','290','291','292','294','295','296','297','298','324','325','326','327','340','341','342','343','344','345','346','347','348','370','371','372','373','374','376') -- Europe
			SET @LeadRank = 151        
		ELSE IF @Country IN ('35','187','271','272','288','308','246') -- GCC
			SET @LeadRank = 152
		ELSE IF @Country IN ('36','47','55','154','164','180','217','218','230','243','280','328','355','384','24','254','313','25','255','256','72') -- SEA
			SET @LeadRank = 153
		ELSE IF @Country IN ('375') -- UAE
			SET @LeadRank = 154
		ELSE IF @Country IN ('12','44','63','144','231','378') -- US/Canada
			SET @LeadRank = 155
		ELSE
			SET @LeadRank = 152
	END 
--  ELSE IF @UtmSource='whatsapp_crm_sales' AND @Utmcampaign like '%whatsapp%crm%trad%' AND @InsurerId > 0 
--         SET @LeadRank = 58 
 /*
 ELSE IF @IsNRI=1 AND (
						(@Utmcampaign LIKE '%Annuity%')
						OR (@Age >= 55 AND @InvType=2 AND @IsImmediatePension=1)
					  )		 
		SET @LeadRank = 35		*/			
 ELSE IF  @IsNRI=1 
		BEGIN
				IF @Country IN ('36','47','55','154','164','180','217','218','230','243','280','328','355','384','24','254','313','25','255','256','72')  --Hungry Hunters ANZ	
					SET @LeadRank = 71 --score based
				ELSE IF @Country IN ('12','44','63','144','231','378','360','179') --US/CANADA
					SET @LeadRank = 74 --SCORE BASED
				ELSE IF @Country IN ('3','4','5','10','26','27','28','29','30','31','32','33','38','39','40','41','42','43','49','50','56','57','81','82','87','88','89','92','93','94','95','101','113','114','115','116','117','118','119','120','121','127','128','129','130','131','132','133','134','135','136','140','141','142','143','155','156','157','158','159','160','161','167','168','169','170','171','173','174','176','195','203','204','205','207','208','209','234','247','248','249','250','251','252','266','267','268','269','270','281','282','283','284','285','289','290','291','292','294','295','296','297','298','324','325','326','327','340','341','342','343','344','345','346','347','348','370','371','372','373','374','376','390','262','172') --europe
					SET @LeadRank = 81 -- SCORE BASED
				ELSE IF @Country IN ('375') AND @LeadSource='PBMOBILEAPP' AND @UtmMedium LIKE '%NRI%APP%' --UAE
					SET @LeadRank = 31		-- score based		
                ELSE IF @Country IN ('375')--Gulf
					SET @LeadRank = 77		-- score based		
				ELSE
					SET @LeadRank = 31   -- score based
		END	   
 ELSE IF @UtmSource='whatsapp_crm_sales'  AND ISNULL(@Utmcampaign,'') NOT LIKE '%regional%' AND @AnnualIncome < 300000
        SET @LeadRank = 59
 ELSE IF @UtmSource='whatsapp_crm_sales' AND @InsurerId > 0 AND ISNULL(@Utmcampaign,'') NOT LIKE '%regional%' AND @CityID IN (551,555)
        SET @LeadRank = 280       
 ELSE IF @UtmSource='whatsapp_crm_sales' AND (@Prebooking=1 OR @InsurerId > 0) AND ISNULL(@Utmcampaign,'') NOT LIKE '%regional%'
		SET @LeadRank = 67	      
 ELSE IF @UtmSource='whatsapp_crm_sales' AND @Source='1' AND ISNULL(@Utmcampaign,'') NOT LIKE '%regional%'
         SET @LeadRank = 68        
 ELSE IF @UtmSource='whatsapp_crm_sales' AND ISNULL(@Utmcampaign,'') NOT LIKE '%regional%'
		SET @LeadRank = 69	 
 
 /*
 ELSE IF  @AnnualIncome >= 1500000 AND @InvType IN (1,4,5)
			SET @LeadRank = 81
 ELSE IF  @AnnualIncome >= 1500000 AND @InvType IN (3)
			SET @LeadRank = 82	
 ELSE IF  @AnnualIncome >= 1500000 AND @InvType IN (2)
			SET @LeadRank = 83 					
 */
 /*ELSE IF  (@PlanFType='T' OR @UtmSource = '%CRMSMS_Traditional%') AND @InsurerId=7
		 SET @LeadRank = 99*/
--  ELSE IF  @IsNRI=0 AND @InvType = 6 AND @PlanFType='T' 
-- 		 SET @LeadRank = 25        
 ELSE IF  @IsNRI=0 AND @InvType <> 3 AND (@PlanFType='T' OR @UtmSource LIKE '%CRMSMS_Traditional%') AND ISNULL(@UtmSource,'') <> 'CRMSMS_T_Retainer'
        BEGIN
            IF (@AnnualIncome >=1500000) AND @Age >=22 AND ISNULL(@CityID,0) NOT IN (207,411,499,420,6,837,853,854,541,551,553,554,555,556,302,309,316,666,234,564,103,111,801,360,895,363) AND ISNULL(@StateID,0) NOT IN (1,16,20,17,30,37,11,25)
                BEGIN
                    SET @LeadRank = 225 -- SCORE BASED , 225,226,227,228
                END
            ELSE 
                SET @LeadRank = 91 -- SCORE BASED , 91,92,93,94
        END		 
 ELSE IF  @UtmSource='CRM_SMS_Retainer_BALIC' AND @UtmTerm='Talktime' AND ISNULL(@Source,'') NOT IN ('1','2')
		SET @LeadRank = 14
--  ELSE IF  (@UtmSource='CRM_SMS_Retainer_BALIC' AND ISNULL(@Source,'') NOT IN ('1','2'))		  
-- 		SET @LeadRank = 15	
 ELSE IF  @UtmSource = 'CRMSMS_T_Retainer' AND ISNULL(@Source,'') IN ('0')
		SET @LeadRank = 66				
--  ELSE IF  (@UtmSource = 'CRMSMS_T_Retainer' AND ISNULL(@Source,'') IN ('1','2'))
-- 		SET @LeadRank = 16	
 --ELSE IF  @UtmSource LIKE '%Retainer%' OR @UtmSource LIKE '%Paisa%'
	--	SET @LeadRank = 17	
 ELSE IF  (@Utmcampaign LIKE '%Annuity%') OR @UtmSource='Annuity_Niveshkar_Befikar' OR (@Age > 55 AND @InvType=2 AND @IsImmediatePension=1) AND ISNULL(@UtmSource,'') <> 'Paisa_Bureau_Annuity' --OR (@Age > 50 AND @InvType=2) 
		SET @LeadRank = 45
 ELSE IF  @LeadSource='pbmobileapp' AND @UtmMedium='utm_Medium = growth_app_is_annuity'
		SET @LeadRank = 45       
 ELSE IF  @Source <> '0' AND @UtmSource LIKE '%CRMSMS%'
		SET @LeadRank = 69
/*						
 ELSE IF @InvType=5						
		SET @LeadRank = 70
*/				
 /*HNI*/
 /*
 ELSE IF @InvType=1 AND  @PlanFType='SIP'--@InvType NOT IN (2,3) AND @Brandname in ('Apple','OnePlus','Huawei','Google','Asus') AND @Age >= 30
			SET @LeadRank = 80
 */
 --ELSE IF @InvType IN (3) AND @Utmcampaign IN ('Child_SSA00Sukanya_Yojana','Child_Plan_New_Exact_RLSA00Child_Education-Saving_Plan') 
	--	SET @LeadRank = 25
 ELSE IF @InvType IN (3) --AND @LeadSource <> 'PBMOBILEAPP'  /* Growth New Logic*/
	 BEGIN
		IF		(@InsurerId > 0 AND @LeadSource = 'PB' AND (ISNULL(@UtmSource,'') IN ('',NULL,'myacc','direct')))
			OR  (@RepeatCust > 0 AND @Prebooking =1)
			OR  (@Prebooking = 1)
			OR  (@InsurerId > 0 AND @LeadSource IN ('PB','PBMobile') AND (ISNULL(@UtmSource,'') IN ('google_brand','yahoo_brand','organic','',NULL,'myacc','direct')))
			OR  (@InsurerId > 0 AND @RepeatCust > 0 AND (ISNULL(@UtmSource,'') NOT IN ('google_brand','yahoo_brand','organic','CRMSMS','CRMPmailer')))
			BEGIN				
				SET @LeadRank = 21											
			END	
		ELSE IF	   (@LeadSource = 'PB')
				OR (@InsurerId > 0 AND @LeadSource IN ('PBMobile') AND (ISNULL(@UtmSource,'') IN ('google','facebook','bing','yahoo','g,youtube','',NULL,'myacc','direct')))				
					BEGIN
						SET @LeadRank =22
					END
		ELSE IF	   (@InsurerId > 0 AND (ISNULL(@UtmSource,'') IN ('google_brand','yahoo_brand','organic','google','facebook','bing','yahoo','g,youtube','',NULL,'myacc','direct')))
				OR (@InsurerId > 0)	
				OR (@LeadSource = 'PBMobile' AND (ISNULL(@UtmSource,'') IN ('google_brand','yahoo_brand','organic','',NULL,'myacc','direct')))			
					BEGIN
						SET @LeadRank =23
					END	
		ELSE 
			BEGIN
				SET @LeadRank =24
			END	
	 END		
  ELSE IF @InvType IN (2)
	  BEGIN
		IF		(@InsurerId > 0 AND @LeadSource = 'PB' AND (ISNULL(@UtmSource,'') IN ('',NULL,'myacc','direct')))
			OR  (@RepeatCust > 0 AND @Prebooking =1)
			OR  (@Prebooking = 1)
			OR  (@InsurerId > 0 AND @LeadSource IN ('PB','PBMobile') AND (ISNULL(@UtmSource,'') IN ('google_brand','yahoo_brand','organic','',NULL,'myacc','direct')))
			OR  (@InsurerId > 0 AND @RepeatCust > 0 AND (ISNULL(@UtmSource,'') NOT IN ('google_brand','yahoo_brand','organic','CRMSMS','CRMPmailer')))
			BEGIN				
				SET @LeadRank = 41											
			END	
		ELSE IF	   (@LeadSource = 'PB')
				OR (@InsurerId > 0 AND @LeadSource IN ('PBMobile') AND (ISNULL(@UtmSource,'') IN ('google','facebook','bing','yahoo','g,youtube','',NULL,'myacc','direct')))				
					BEGIN
						SET @LeadRank =42
					END
		ELSE IF	   (@InsurerId > 0 AND (ISNULL(@UtmSource,'') IN ('google_brand','yahoo_brand','organic','google','facebook','bing','yahoo','g,youtube','',NULL,'myacc','direct')))
				OR (@InsurerId > 0)	
				OR (@LeadSource = 'PBMobile' AND (ISNULL(@UtmSource,'') IN ('google_brand','yahoo_brand','organic','',NULL,'myacc','direct')))			
					BEGIN
						SET @LeadRank =43
					END	
		ELSE 
			BEGIN
				SET @LeadRank =44
			END	
	 END			 
  ELSE 
	 BEGIN
        IF @AnnualIncome >=1500000 AND @Age >=22 AND ISNULL(@CityID,0) IN (103,654,256,492,303,76,124,414,454,455,103,256,76,124,306,303,243,54,455,527,525,126) 
            BEGIN
                SET @LeadRank = 207 -- score based (207,208,209,210)
            END    
        ELSE IF  @AnnualIncome >=1500000 AND @Age >=22 AND ISNULL(@CityID,0) NOT IN (207,411,499,420,6,837,853,854,541,551,553,554,555,556,302,316,666,309,234,564,141,401,865,103,111,801,360,895,363) AND ISNULL(@StateID,0) NOT IN (1,16,17,20,30,37,6,12,27,11,25)
            BEGIN
                SET @LeadRank = 207
            END            	                      
        ELSE IF		(@InsurerId > 0 AND @LeadSource = 'PB' AND (ISNULL(@UtmSource,'') IN ('',NULL,'myacc','direct')))
			OR  (@RepeatCust > 0 AND @Prebooking =1)
			OR  (@Prebooking = 1)
			OR  (@InsurerId > 0 AND @LeadSource IN ('PB','PBMobile') AND (ISNULL(@UtmSource,'') IN ('google_brand','yahoo_brand','organic','',NULL,'myacc','direct')))
			OR  (@InsurerId > 0 AND @RepeatCust > 0 AND (ISNULL(@UtmSource,'') NOT IN ('google_brand','yahoo_brand','organic','CRMSMS','CRMPmailer')))
			BEGIN	
                IF @SeachPlanID IN (15768,15757,32684) AND @IsNRI=0	
                    BEGIN
                        IF @Age >= 40
                            SET @LeadRank = 271
                        ELSE    
                            SET @LeadRank = 221
                    END		                    
                ELSE    
				    SET @LeadRank = 1											
			END	
		ELSE IF	   (@LeadSource = 'PB')
				OR (@InsurerId > 0 AND @LeadSource IN ('PBMobile') AND (ISNULL(@UtmSource,'') IN ('google','facebook','bing','yahoo','g,youtube','',NULL,'myacc','direct')))				
					BEGIN
                        IF @SeachPlanID IN (15768,15757,32684) AND @IsNRI=0			
                            BEGIN
                                IF @Age >= 40
                                    SET @LeadRank = 271
                                ELSE    
                                    SET @LeadRank = 222
                            END	
                        ELSE    
						    SET @LeadRank =2
					END
		ELSE IF	   (@InsurerId > 0 AND (ISNULL(@UtmSource,'') IN ('google_brand','yahoo_brand','organic','google','facebook','bing','yahoo','g,youtube','',NULL,'myacc','direct')))
				OR (@InsurerId > 0)	
				OR (@LeadSource = 'PBMobile' AND (ISNULL(@UtmSource,'') IN ('google_brand','yahoo_brand','organic','',NULL,'myacc','direct')))			
					BEGIN
						IF @SeachPlanID IN (15768,15757,32684) AND @IsNRI=0			
                            BEGIN
                                IF @Age >= 40
                                    SET @LeadRank = 271
                                ELSE    
                                    SET @LeadRank = 223
                            END	
                        ELSE    
						    SET @LeadRank =3
					END	
		ELSE 
			BEGIN
				IF @SeachPlanID IN (15768,15757,32684) AND @IsNRI=0			
                    BEGIN
                        IF @Age >= 40
                            SET @LeadRank = 271
                        ELSE    
                            SET @LeadRank = 223
                    END	
                ELSE    
                    SET @LeadRank =4
			END	
	 END			 
			
 return @LeadRank

END
------------------


