CREATE Proc [CRM].[GetLinksBySectionID]
(
@SectionId varchar(200)
)
As
Begin
	SELECT MenuId,MenuName,URL,ParentId,SerialNo,CreatedOn,HasChildren,IsActive
	FROM CRM.FunctionsMaster
	where parentid = (select MenuId from 
	CRM.FunctionsMaster where menuname=@SectionId
	) and IsActive=1
	and MenuId NOT IN (
	select MenuId from CRM.FunctionsMaster  
	where MenuName in('FosAllocationPanel','FOSCityPanel','FosAllocationPanelV2'))
End