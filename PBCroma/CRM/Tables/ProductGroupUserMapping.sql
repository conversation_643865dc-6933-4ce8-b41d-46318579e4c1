CREATE TABLE [CRM].[ProductGroupUserMapping] (
    [Id]        INT      IDENTITY (1, 1) NOT FOR REPLICATION NOT NULL,
    [ProductId] TINYINT  NULL,
    [GroupId]   SMALLINT NULL,
    [UserId]    BIGINT   NULL,
    [IsActive]  BIT      NULL,
    CONSTRAINT [PK_ProductGroupUserMapping] PRIMARY KEY CLUSTERED ([Id] ASC) WITH (FILLFACTOR = 90) ON [PRIMARY],
    CONSTRAINT [FK_ProductGroupUserMapping_GroupId] FOREIGN KEY ([GroupId]) REFERENCES [CRM].[UserGroupMaster] ([UserGroupID]),
    CONSTRAINT [FK_ProductGroupUserMapping_ProductId] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[Products] ([ID])
) ON [PRIMARY];

