CREATE TABLE [CRM].[UserGroupMaster] (
    [User<PERSON>roup<PERSON>]            SMALLINT      IDENTITY (1, 1) NOT FOR REPLICATION NOT NULL,
    [UserGroupName]          VARCHAR (200) NULL,
    [IsAutoAllocationActive] BIT           NULL,
    [IsBMSGroup]             BIT           DEFAULT ((0)) NOT NULL,
    [IsAsterick]             BIT           NULL,
    [BMSPoolID]              TINYINT       NULL,
    [GroupCode]              VARCHAR (200) NULL,
    [Contex]                 VARCHAR (50)  NULL,
    [Queues]                 VARCHAR (50)  NULL,
    [PolicyType]             TINYINT       NULL,
    [IsOneLead]              BIT           DEFAULT ((0)) NULL,
    [IsProgressive]          BIT           NULL,
    [Asterisk_IP]            VARCHAR (25)  NULL,
    [Asterisk_Url]           VARCHAR (100) NULL,
    [StatusMappingCode]      VARCHAR (20)  NULL,
    [ProcessID]              SMALLINT      NULL,
    [IsSOS]                  BIT           DEFAULT ((0)) NULL,
    [PauseTimer]             INT           NULL,
    [IsWebphone]             BIT           NULL,
    [GroupVirtualNo]         BIGINT        NULL,
    [GroupVirtualComapany]   VARCHAR (50)  NULL,
    [CreatedOn]              DATETIME      DEFAULT (getdate()) NULL,
    [UpdatedOn]              DATETIME      NULL,
    [ServiceRoleTypeId]      TINYINT       NULL,
    [AllowGroupMails]        BIT           NULL,
    [HasWebQueue]            BIT           NULL,
    [AllowOffHoursCallback]  BIT           NULL,
    [GroupTypeId]            TINYINT       NULL,
    [GroupLanguage]          VARCHAR (50)  NULL,
    [SuperGroupId]           SMALLINT      NULL,
    [LanguageID]             SMALLINT      NULL,
    [Location]               VARCHAR (50)  NULL,
    [FloorProcess]           VARCHAR (50)  NULL,
    CONSTRAINT [PK_UserGroupMaster] PRIMARY KEY CLUSTERED ([UserGroupID] ASC) WITH (FILLFACTOR = 90) ON [PRIMARY],
    CONSTRAINT [FK_UserGroupMaster_UserGroupMaster] FOREIGN KEY ([UserGroupID]) REFERENCES [CRM].[UserGroupMaster] ([UserGroupID])
) ON [PRIMARY];


GO
CREATE NONCLUSTERED INDEX [Inx_context__usgid_gvno_grvicom]
    ON [CRM].[UserGroupMaster]([Contex] ASC)
    INCLUDE([UserGroupID], [GroupVirtualNo], [GroupVirtualComapany]) WITH (FILLFACTOR = 90)
    ON [CDC];


GO
CREATE NONCLUSTERED INDEX [Inx_groupvirtualno__ug_Q]
    ON [CRM].[UserGroupMaster]([GroupVirtualNo] ASC)
    INCLUDE([UserGroupID], [Queues]) WITH (FILLFACTOR = 90)
    ON [CDC];


GO
CREATE NONCLUSTERED INDEX [IX_UserGroupMaster_StatusMappingCode]
    ON [CRM].[UserGroupMaster]([StatusMappingCode] ASC)
    INCLUDE([UserGroupID]) WITH (FILLFACTOR = 90)
    ON [PRIMARY];


GO
CREATE NONCLUSTERED INDEX [_IX_GroupCode]
    ON [CRM].[UserGroupMaster]([GroupCode] ASC)
    INCLUDE([UserGroupID], [UserGroupName]) WITH (FILLFACTOR = 90)
    ON [PRIMARY];


GO
CREATE NONCLUSTERED INDEX [_IX_UserGroupName]
    ON [CRM].[UserGroupMaster]([UserGroupName] ASC, [UserGroupID] ASC) WITH (FILLFACTOR = 90)
    ON [PRIMARY];


GO
CREATE NONCLUSTERED INDEX [IX_NCL_CRM_UserGroupMaster]
    ON [CRM].[UserGroupMaster]([IsBMSGroup] ASC)
    INCLUDE([UserGroupID], [UserGroupName], [IsAutoAllocationActive], [IsAsterick]) WITH (FILLFACTOR = 90)
    ON [PRIMARY];


GO
CREATE NONCLUSTERED INDEX [INX_UGM_GroupID_GName]
    ON [CRM].[UserGroupMaster]([UserGroupID] ASC)
    INCLUDE([UserGroupName], [ProcessID]) WITH (FILLFACTOR = 90)
    ON [Secondary];


GO
DENY SELECT
    ON [CRM].[UserGroupMaster] ([IsWebphone]) TO [Arunchaudhary]
    AS [dbo];

