CREATE TABLE [CRM].[ProductGroupMapping] (
    [ProductGroupId]   SMALLINT IDENTITY (1, 1) NOT FOR REPLICATION NOT NULL,
    [GroupId]          SMALLINT NULL,
    [ProductId]        SMALLINT NULL,
    [SubProductTypeId] SMALLINT NULL,
    CONSTRAINT [PK_ProductGroupMapping] PRIMARY KEY CLUSTERED ([ProductGroupId] ASC) WITH (FILLFACTOR = 90) ON [PRIMARY]
) ON [PRIMARY];


GO
CREATE NONCLUSTERED INDEX [IX_ProductGroupMapping_GroupId]
    ON [CRM].[ProductGroupMapping]([GroupId] ASC)
    INCLUDE([ProductId]) WITH (FILLFACTOR = 90)
    ON [PRIMARY];

