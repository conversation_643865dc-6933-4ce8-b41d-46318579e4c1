CREATE TABLE [CRM].[Leaddetails] (
    [LeadID]             BIGINT          NOT NULL,
    [SessionID]          VARCHAR (50)    NULL,
    [Name]               VARCHAR (50)    NOT NULL,
    [Gender]             VARCHAR (50)    NULL,
    [DOB]                VARCHAR (50)    NOT NULL,
    [MobileNo]           VARCHAR (50)    NOT NULL,
    [AltPhoneNo]         VARCHAR (50)    NULL,
    [EmailID]            VARCHAR (100)   NULL,
    [Address]            VARCHAR (200)   NULL,
    [CityID]             VARCHAR (50)    NOT NULL,
    [StateID]            TINYINT         NOT NULL,
    [PostCode]           VARCHAR (50)    NULL,
    [Country]            VARCHAR (50)    NULL,
    [MaritalStatus]      VARCHAR (50)    NULL,
    [AnnualIncome]       VARCHAR (50)    NULL,
    [ProfessionId]       TINYINT         NULL,
    [LeadSource]         VARCHAR (25)    NULL,
    [TrackingID]         BIGINT          NULL,
    [ExitID]             BIGINT          NULL,
    [HighestBookingStep] INT             NULL,
    [ExitPointURL]       VARCHAR (500)   NULL,
    [Utm_source]         VARCHAR (255)   NULL,
    [UTM_Medium]         VARCHAR (255)   NULL,
    [Utm_term]           VARCHAR (255)   NULL,
    [Utm_campaign]       VARCHAR (255)   NULL,
    [NewsletterOffers]   INT             NULL,
    [ProductID]          TINYINT         NOT NULL,
    [CustomerID]         BIGINT          NULL,
    [IsNDNC]             BIT             NULL,
    [IsReleased]         BIT             NULL,
    [CreatedON]          DATETIME        NOT NULL,
    [UpdatedON]          DATETIME        CONSTRAINT [DF_date1] DEFAULT (getdate()) NULL,
    [ParentID]           BIGINT          NULL,
    [IsActive]           BIT             NULL,
    [lastVisitedOn]      DATETIME        NULL,
    [LeadRank]           SMALLINT        CONSTRAINT [DF_Leaddetails_LeadRank1] DEFAULT ((3)) NULL,
    [ReferralID]         BIGINT          NULL,
    [AddOnParentID]      BIGINT          NULL,
    [PolicyType]         SMALLINT        NULL,
    [ProductType]        SMALLINT        NULL,
    [HasAddOn]           BIT             NULL,
    [IsCTC]              BIT             NULL,
    [LeadScore]          DECIMAL (9, 1)  NULL,
    [LeadGrade]          SMALLINT        NULL,
    [DateOfBirth]        DATE            NULL,
    [FutureProsDate]     DATE            NULL,
    [MKTRevenue]         DECIMAL (18, 3) NULL,
    [LeadType]           VARCHAR (20)    NULL,
    [IsSMS]              BIT             NULL,
    [Model_Score]        FLOAT (53)      NULL,
    [Model_premium]      DECIMAL (18, 3) NULL,
    [ChatStatus]         TINYINT         DEFAULT ((0)) NULL,
    [IsAllocable]        BIT             DEFAULT ((0)) NOT NULL,
    [assignedTO]         SMALLINT        NULL,
    [IsNameAbused]       BIT             NULL,
    [IsPrimary]          BIT             DEFAULT ((0)) NOT NULL,
    [IsMarkExchange]     TINYINT         CONSTRAINT [DF_leaddetails_IsMarkExchange1] DEFAULT ((0)) NULL,
    [Source]             VARCHAR (50)    NULL,
    [LeadCreationSource] VARCHAR (100)   NULL,
    [utm_content]        VARCHAR (255)   NULL,
    [EnquiryId]          BIGINT          NULL,
    [EncryptionText]     VARCHAR (500)   NULL,
    [EncryptedLeadId]    VARCHAR (100)   NULL,
    [encMobile]          VARCHAR (200)   NULL,
    [encEmail]           VARCHAR (200)   NULL,
    [TenantId]           SMALLINT        NULL,
    [BusinessPartnerId]  INT             NULL,
    [IsBookedLead]       BIT             NULL,
    CONSTRAINT [PK_InsrtLeaddetailsPB11] PRIMARY KEY CLUSTERED ([LeadID] ASC) WITH (FILLFACTOR = 90) ON [Secondary]
) ON [Secondary];


GO
CREATE NONCLUSTERED INDEX [NIX_Mobile_productid]
    ON [CRM].[Leaddetails]([MobileNo] ASC, [ProductID] ASC) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [NIX_Mobile_Createdon_LeadSource]
    ON [CRM].[Leaddetails]([MobileNo] ASC, [LeadSource] ASC, [CreatedON] ASC) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_Utm_source_Utm_term_CreatedON_02625]
    ON [CRM].[Leaddetails]([Utm_source] ASC, [Utm_term] ASC, [CreatedON] ASC)
    INCLUDE([LeadID], [ProductID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_prod_utmsrc_create_leaddetails]
    ON [CRM].[Leaddetails]([ProductID] ASC, [Utm_source] ASC, [CreatedON] ASC)
    INCLUDE([LeadID], [SessionID], [MobileNo]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_ParentID_Country_LeadSource_ProductID_CreatedON_1602B]
    ON [CRM].[Leaddetails]([ParentID] ASC, [Country] ASC, [LeadSource] ASC, [ProductID] ASC, [CreatedON] ASC)
    INCLUDE([LeadID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_LeadSource_ProductID_CreatedON_IsActive_LeadID_MobileNO_LeadRank]
    ON [CRM].[Leaddetails]([LeadSource] ASC, [ProductID] ASC, [CreatedON] ASC, [IsActive] ASC)
    INCLUDE([LeadID], [MobileNo], [LeadRank]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_LeadID_Utm_term_Utm_source_UTM_Medium_Utm_campaign_CreatedOn]
    ON [CRM].[Leaddetails]([LeadID] ASC, [Utm_term] ASC, [Utm_source] ASC, [UTM_Medium] ASC, [Utm_campaign] ASC, [CreatedON] ASC) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_LeadID_MobileNo_IsActive]
    ON [CRM].[Leaddetails]([IsActive] ASC, [LeadID] ASC, [MobileNo] ASC) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [ix_LeadID_LeadCreationSource]
    ON [CRM].[Leaddetails]([LeadID] ASC)
    INCLUDE([ReferralID], [LeadCreationSource]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_Leaddetails_ProductID_ParentID_IsActive]
    ON [CRM].[Leaddetails]([ProductID] ASC, [ParentID] ASC, [IsActive] ASC)
    INCLUDE([LeadID], [Utm_campaign], [CreatedON], [UpdatedON]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_Leaddetails_ProductID_MobileNo_LeadSource_CreatedON_IsActive]
    ON [CRM].[Leaddetails]([LeadSource] ASC, [ProductID] ASC, [Utm_source] ASC, [MobileNo] ASC, [IsActive] ASC, [CreatedON] ASC)
    INCLUDE([LeadID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_Leaddetails_ProductID_CreatedON_1]
    ON [CRM].[Leaddetails]([ProductID] ASC, [CreatedON] ASC)
    INCLUDE([LeadID], [LeadSource], [Utm_source], [UTM_Medium], [Utm_campaign], [CustomerID], [MobileNo], [Name], [EmailID], [SessionID], [DOB], [CityID], [PostCode], [AnnualIncome], [UpdatedON], [ParentID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_Leaddetails_ProductID]
    ON [CRM].[Leaddetails]([ProductID] ASC, [CustomerID] ASC, [CreatedON] ASC)
    INCLUDE([LeadID], [MobileNo], [IsActive]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_Leaddetails_ExitPointURL]
    ON [CRM].[Leaddetails]([CustomerID] ASC, [LeadSource] ASC, [ProductID] ASC, [CreatedON] ASC)
    INCLUDE([ExitPointURL], [LeadID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_LeadDetails_AddonPrntID]
    ON [CRM].[Leaddetails]([AddOnParentID] ASC)
    INCLUDE([LeadID], [ProductID], [IsActive]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_AppointMnet_Allocation]
    ON [CRM].[Leaddetails]([ParentID] ASC, [MobileNo] ASC, [LeadSource] ASC, [CreatedON] ASC, [IsActive] ASC)
    INCLUDE([LeadID], [ProductID], [LeadRank]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [INX_UpdatedON]
    ON [CRM].[Leaddetails]([UpdatedON] ASC) WITH (FILLFACTOR = 80)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [INX_SessonID]
    ON [CRM].[Leaddetails]([SessionID] ASC) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
ALTER INDEX [INX_SessonID]
    ON [CRM].[Leaddetails] DISABLE;


GO
CREATE NONCLUSTERED INDEX [INX_ParenTID]
    ON [CRM].[Leaddetails]([ParentID] ASC, [IsPrimary] ASC)
    INCLUDE([LeadID], [Name], [Gender], [DOB], [MobileNo], [AltPhoneNo], [EmailID], [Address], [CityID], [PostCode], [MaritalStatus], [AnnualIncome], [ProfessionId], [LeadSource], [TrackingID], [ExitID], [HighestBookingStep], [ExitPointURL], [Utm_source], [ProductID], [CreatedON], [UpdatedON], [CustomerID], [IsCTC]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [INx_moble_Actve]
    ON [CRM].[Leaddetails]([MobileNo] ASC, [IsActive] ASC, [CreatedON] ASC)
    INCLUDE([LeadID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [INX_MObileNo_IsActive]
    ON [CRM].[Leaddetails]([MobileNo] ASC, [IsActive] ASC)
    INCLUDE([LeadID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [INx_Mobile]
    ON [CRM].[Leaddetails]([MobileNo] ASC, [CustomerID] ASC)
    INCLUDE([DOB]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [INX_LEADID_CID_N_G_M_A_E_A_PC]
    ON [CRM].[Leaddetails]([LeadID] ASC, [CityID] ASC)
    INCLUDE([Name], [Gender], [MobileNo], [AltPhoneNo], [EmailID], [Address], [PostCode], [ParentID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [INX_CustID_MblNO]
    ON [CRM].[Leaddetails]([CustomerID] ASC)
    INCLUDE([LeadID], [ProductID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [INX_Cretedon_ISActve]
    ON [CRM].[Leaddetails]([CreatedON] ASC, [LeadID] ASC, [IsActive] ASC) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [_IX_LeadDetatils_Agentmenus]
    ON [CRM].[Leaddetails]([LeadID] ASC, [ProductID] ASC, [MobileNo] ASC, [LeadSource] ASC) WITH (FILLFACTOR = 80)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [_IX_lastVisitedOn_LeadID]
    ON [CRM].[Leaddetails]([lastVisitedOn] DESC, [LeadID] ASC)
    INCLUDE([IsActive], [Name], [ProductID], [MobileNo], [CreatedON]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [_IX_EmailId]
    ON [CRM].[Leaddetails]([EmailID] ASC, [CreatedON] ASC)
    INCLUDE([MobileNo], [LeadID], [Name], [CityID], [LeadSource], [Utm_source], [ProductID], [CustomerID], [UpdatedON], [ParentID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE STATISTICS [_WA_Sys_00000044_4F7CD00D]
    ON [CRM].[Leaddetails]([IsBookedLead]);


GO
CREATE STATISTICS [_WA_Sys_00000016_338A9CD5]
    ON [CRM].[Leaddetails]([Utm_source]);


GO
CREATE STATISTICS [_WA_Sys_00000017_338A9CD5]
    ON [CRM].[Leaddetails]([UTM_Medium]);


GO
CREATE STATISTICS [_WA_Sys_0000003D_338A9CD5]
    ON [CRM].[Leaddetails]([utm_content]);


GO
CREATE STATISTICS [_WA_Sys_00000019_338A9CD5]
    ON [CRM].[Leaddetails]([Utm_campaign]);


GO
CREATE STATISTICS [_WA_Sys_00000020_338A9CD5]
    ON [CRM].[Leaddetails]([UpdatedON]);


GO
CREATE STATISTICS [_WA_Sys_00000012_338A9CD5]
    ON [CRM].[Leaddetails]([TrackingID]);


GO
CREATE STATISTICS [_WA_Sys_00000043_338A9CD5]
    ON [CRM].[Leaddetails]([TenantId]);


GO
CREATE STATISTICS [_WA_Sys_0000000B_338A9CD5]
    ON [CRM].[Leaddetails]([StateID]);


GO
CREATE STATISTICS [_WA_Sys_0000003B_338A9CD5]
    ON [CRM].[Leaddetails]([Source]);


GO
CREATE STATISTICS [_WA_Sys_00000002_338A9CD5]
    ON [CRM].[Leaddetails]([SessionID]);


GO
CREATE STATISTICS [_WA_Sys_00000025_338A9CD5]
    ON [CRM].[Leaddetails]([ReferralID]);


GO
CREATE STATISTICS [_dta_stat_864722133_16_11]
    ON [CRM].[Leaddetails]([ProfessionId]);


GO
CREATE STATISTICS [_WA_Sys_00000028_338A9CD5]
    ON [CRM].[Leaddetails]([ProductType]);


GO
CREATE STATISTICS [_WA_Sys_0000001B_338A9CD5]
    ON [CRM].[Leaddetails]([ProductID]);


GO
CREATE STATISTICS [_WA_Sys_0000000C_338A9CD5]
    ON [CRM].[Leaddetails]([PostCode]);


GO
CREATE STATISTICS [_WA_Sys_00000027_338A9CD5]
    ON [CRM].[Leaddetails]([PolicyType]);


GO
CREATE STATISTICS [_WA_Sys_0000001A_338A9CD5]
    ON [CRM].[Leaddetails]([NewsletterOffers]);


GO
CREATE STATISTICS [_WA_Sys_00000003_338A9CD5]
    ON [CRM].[Leaddetails]([Name]);


GO
CREATE STATISTICS [_WA_Sys_00000033_338A9CD5]
    ON [CRM].[Leaddetails]([Model_Score]);


GO
CREATE STATISTICS [_WA_Sys_00000034_338A9CD5]
    ON [CRM].[Leaddetails]([Model_premium]);


GO
CREATE STATISTICS [_WA_Sys_00000006_338A9CD5]
    ON [CRM].[Leaddetails]([MobileNo]);


GO
CREATE STATISTICS [_WA_Sys_0000002F_338A9CD5]
    ON [CRM].[Leaddetails]([MKTRevenue]);


GO
CREATE STATISTICS [_WA_Sys_0000000E_338A9CD5]
    ON [CRM].[Leaddetails]([MaritalStatus]);


GO
CREATE STATISTICS [_WA_Sys_00000031_338A9CD5]
    ON [CRM].[Leaddetails]([LeadType]);


GO
CREATE STATISTICS [_WA_Sys_0000002B_338A9CD5]
    ON [CRM].[Leaddetails]([LeadScore]);


GO
CREATE STATISTICS [_WA_Sys_00000024_338A9CD5]
    ON [CRM].[Leaddetails]([LeadRank]);


GO
CREATE STATISTICS [ST_C_LId_1]
    ON [CRM].[Leaddetails]([LeadID]);


GO
CREATE STATISTICS [_dta_stat_864722133_16_1]
    ON [CRM].[Leaddetails]([LeadID]);


GO
CREATE STATISTICS [_WA_Sys_0000002C_338A9CD5]
    ON [CRM].[Leaddetails]([LeadGrade]);


GO
CREATE STATISTICS [_WA_Sys_0000003C_338A9CD5]
    ON [CRM].[Leaddetails]([LeadCreationSource]);


GO
CREATE STATISTICS [_WA_Sys_00000023_338A9CD5]
    ON [CRM].[Leaddetails]([lastVisitedOn]);


GO
CREATE STATISTICS [_WA_Sys_00000032_338A9CD5]
    ON [CRM].[Leaddetails]([IsSMS]);


GO
CREATE STATISTICS [_WA_Sys_0000001E_338A9CD5]
    ON [CRM].[Leaddetails]([IsReleased]);


GO
CREATE STATISTICS [_WA_Sys_00000039_338A9CD5]
    ON [CRM].[Leaddetails]([IsPrimary]);


GO
CREATE STATISTICS [_WA_Sys_0000001D_338A9CD5]
    ON [CRM].[Leaddetails]([IsNDNC]);


GO
CREATE STATISTICS [_WA_Sys_00000038_338A9CD5]
    ON [CRM].[Leaddetails]([IsNameAbused]);


GO
CREATE STATISTICS [_WA_Sys_0000003A_338A9CD5]
    ON [CRM].[Leaddetails]([IsMarkExchange]);


GO
CREATE STATISTICS [_WA_Sys_0000002A_338A9CD5]
    ON [CRM].[Leaddetails]([IsCTC]);


GO
CREATE STATISTICS [_WA_Sys_00000036_338A9CD5]
    ON [CRM].[Leaddetails]([IsAllocable]);


GO
CREATE STATISTICS [_WA_Sys_00000022_338A9CD5]
    ON [CRM].[Leaddetails]([IsActive]);


GO
CREATE STATISTICS [_WA_Sys_00000014_338A9CD5]
    ON [CRM].[Leaddetails]([HighestBookingStep]);


GO
CREATE STATISTICS [_WA_Sys_00000029_338A9CD5]
    ON [CRM].[Leaddetails]([HasAddOn]);


GO
CREATE STATISTICS [_WA_Sys_00000004_338A9CD5]
    ON [CRM].[Leaddetails]([Gender]);


GO
CREATE STATISTICS [_WA_Sys_0000002E_338A9CD5]
    ON [CRM].[Leaddetails]([FutureProsDate]);


GO
CREATE STATISTICS [_WA_Sys_00000015_338A9CD5]
    ON [CRM].[Leaddetails]([ExitPointURL]);


GO
CREATE STATISTICS [_WA_Sys_00000013_338A9CD5]
    ON [CRM].[Leaddetails]([ExitID]);


GO
CREATE STATISTICS [_WA_Sys_0000003E_338A9CD5]
    ON [CRM].[Leaddetails]([EnquiryId]);


GO
CREATE STATISTICS [_WA_Sys_0000003F_338A9CD5]
    ON [CRM].[Leaddetails]([EncryptionText]);


GO
CREATE STATISTICS [_WA_Sys_00000040_338A9CD5]
    ON [CRM].[Leaddetails]([EncryptedLeadId]);


GO
CREATE STATISTICS [_WA_Sys_00000041_338A9CD5]
    ON [CRM].[Leaddetails]([encMobile]);


GO
CREATE STATISTICS [_WA_Sys_00000042_338A9CD5]
    ON [CRM].[Leaddetails]([encEmail]);


GO
CREATE STATISTICS [_WA_Sys_00000008_338A9CD5]
    ON [CRM].[Leaddetails]([EmailID]);


GO
CREATE STATISTICS [_WA_Sys_00000005_338A9CD5]
    ON [CRM].[Leaddetails]([DOB]);


GO
CREATE STATISTICS [_WA_Sys_0000002D_338A9CD5]
    ON [CRM].[Leaddetails]([DateOfBirth]);


GO
CREATE STATISTICS [_WA_Sys_0000001C_338A9CD5]
    ON [CRM].[Leaddetails]([CustomerID]);


GO
CREATE STATISTICS [_WA_Sys_0000001F_338A9CD5]
    ON [CRM].[Leaddetails]([CreatedON]);


GO
CREATE STATISTICS [_WA_Sys_0000000D_338A9CD5]
    ON [CRM].[Leaddetails]([Country]);


GO
CREATE STATISTICS [ST_C_LId]
    ON [CRM].[Leaddetails]([CityID]);


GO
CREATE STATISTICS [_WA_Sys_0000000A_338A9CD5]
    ON [CRM].[Leaddetails]([CityID]);


GO
CREATE STATISTICS [_dta_stat_864722133_1_27_34_10]
    ON [CRM].[Leaddetails]([CityID]);


GO
CREATE STATISTICS [_WA_Sys_00000035_338A9CD5]
    ON [CRM].[Leaddetails]([ChatStatus]);


GO
CREATE STATISTICS [_WA_Sys_00000044_338A9CD5]
    ON [CRM].[Leaddetails]([BusinessPartnerId]);


GO
CREATE STATISTICS [_WA_Sys_00000037_338A9CD5]
    ON [CRM].[Leaddetails]([assignedTO]);


GO
CREATE STATISTICS [_WA_Sys_0000000F_338A9CD5]
    ON [CRM].[Leaddetails]([AnnualIncome]);


GO
CREATE STATISTICS [_WA_Sys_00000007_338A9CD5]
    ON [CRM].[Leaddetails]([AltPhoneNo]);


GO
CREATE STATISTICS [_WA_Sys_00000009_338A9CD5]
    ON [CRM].[Leaddetails]([Address]);


GO
CREATE STATISTICS [_WA_Sys_00000026_338A9CD5]
    ON [CRM].[Leaddetails]([AddOnParentID]);


GO
CREATE STATISTICS [_WA_Sys_00000018_338A9CD5]
    ON [CRM].[Leaddetails]([Utm_term]);


GO

CREATE TRIGGER [CRM].[trg_Update_LeadDetails]  ON [Matrix].[CRM].[Leaddetails]

FOR UPDATE

AS

	--IF (UPDATE(DOB) OR UPDATE(Name) OR UPDATE(EmailID) OR UPDATE(CityID) OR UPDATE(StateID) OR UPDATE(PostCode) OR UPDATE(Country) 
	--OR UPDATE(ExitPointURL) OR UPDATE(IsNDNC) OR UPDATE(ParentID) OR UPDATE(IsReleased) OR UPDATE(IsActive) OR UPDATE(lastVisitedOn) 
	--OR UPDATE(LeadRank) OR UPDATE(IsCTC) OR UPDATE(DateOfBirth) OR UPDATE(FutureProsDate) OR UPDATE(ChatStatus) OR UPDATE(IsPrimary) 
	--OR UPDATE(IsNameAbused))

BEGIN                                   

	UPDATE A
	SET	A.DOB=B.DOB,A.Name=B.Name,A.EmailID=B.EmailID,A.CityID=B.CityID,A.StateID=B.StateID,A.PostCode=B.PostCode,
		A.Country=B.Country,A.ExitPointURL=B.ExitPointURL,A.IsNDNC=B.IsNDNC,A.ParentID=B.ParentID,A.IsReleased=B.IsReleased,
		A.IsActive=B.IsActive,A.lastVisitedOn=B.lastVisitedOn,A.LeadRank=B.LeadRank,A.IsCTC=B.IsCTC,A.DateOfBirth=B.DateOfBirth,
		A.FutureProsDate=B.FutureProsDate,A.ChatStatus=B.ChatStatus,A.IsPrimary=B.IsPrimary,A.IsNameAbused=B.IsNameAbused, 
		A.MobileNo=B.MobileNo,A.CustomerID=B.CustomerID,A.UpdatedOn=B.UpdatedOn,A.IsMarkExchange=B.IsMarkExchange,A.LeadGrade=B.LeadGrade,
		A.AnnualIncome=B.AnnualIncome,A.Source=B.Source,A.LeadScore=B.LeadScore
	from  matrix.crm.LeadDetails150 (nolock) A
	inner join inserted B ON A.Leadid= B.LeadID                                                                       				
    
	--IF (UPDATE(LeadSource) OR UPDATE(Utm_source) OR UPDATE(UTM_Medium) OR UPDATE(Utm_term) OR UPDATE(Utm_campaign))
	--BEGIN
	--	INSERT INTO [MTX].[LeadLogstracking] (Leadid,LeadSource,CreatedOn,Flag,UTMSource,UTMTerm,UTMMedium,UTMCampaign,HostName,LoggedInUser)
	--	select A.Leadid,A.LeadSource,getdate(),'Trigger',A.Utm_source,A.UTM_Medium,A.Utm_term,A.Utm_campaign,HOST_NAME(),SUSER_NAME() from inserted A
	--END
END
GO
DENY SELECT
    ON [CRM].[Leaddetails] ([encEmail]) TO [denypermission]
    AS [dbo];


GO
DENY SELECT
    ON [CRM].[Leaddetails] ([encMobile]) TO [denypermission]
    AS [dbo];


GO
DENY SELECT
    ON [CRM].[Leaddetails] ([EmailID]) TO [denypermission]
    AS [dbo];


GO
DENY SELECT
    ON [CRM].[Leaddetails] ([AltPhoneNo]) TO [denypermission]
    AS [dbo];


GO
DENY SELECT
    ON [CRM].[Leaddetails] ([MobileNo]) TO [denypermission]
    AS [dbo];


GO
GRANT SELECT
    ON OBJECT::[CRM].[Leaddetails] TO [Audiencedataread]
    AS [dbo];


GO
GRANT SELECT
    ON OBJECT::[CRM].[Leaddetails] TO [ETEC\RahulBhalsodia]
    AS [dbo];


GO
GRANT SELECT
    ON OBJECT::[CRM].[Leaddetails] TO [ASR_prod_read]
    AS [dbo];


GO
GRANT SELECT
    ON OBJECT::[CRM].[Leaddetails] TO [Propensity_models_read]
    AS [dbo];


GO
GRANT SELECT
    ON OBJECT::[CRM].[Leaddetails] TO [Life_CRT_MIS]
    AS [dbo];


GO
GRANT SELECT
    ON OBJECT::[CRM].[Leaddetails] TO [zphinapp]
    AS [dbo];


GO
GRANT UPDATE
    ON OBJECT::[CRM].[Leaddetails] TO [lalan]
    AS [dbo];

