CREATE TABLE [CRM].[leaddetails150] (
    [LeadID]             BIGINT          NOT NULL,
    [SessionID]          VARCHAR (50)    NULL,
    [Name]               VARCHAR (50)    NOT NULL,
    [Gender]             VARCHAR (50)    NULL,
    [DOB]                VARCHAR (50)    NOT NULL,
    [MobileNo]           VARCHAR (50)    NOT NULL,
    [AltPhoneNo]         VARCHAR (50)    NULL,
    [EmailID]            VARCHAR (100)   NULL,
    [Address]            VARCHAR (200)   NULL,
    [CityID]             VARCHAR (50)    NOT NULL,
    [StateID]            TINYINT         NOT NULL,
    [PostCode]           VARCHAR (50)    NULL,
    [Country]            VARCHAR (50)    NULL,
    [MaritalStatus]      VARCHAR (50)    NULL,
    [AnnualIncome]       VARCHAR (50)    NULL,
    [ProfessionId]       TINYINT         NULL,
    [LeadSource]         VARCHAR (25)    NULL,
    [TrackingID]         BIGINT          NULL,
    [ExitID]             BIGINT          NULL,
    [HighestBookingStep] INT             NULL,
    [ExitPointURL]       VARCHAR (500)   NULL,
    [Utm_source]         VARCHAR (255)   NULL,
    [UTM_Medium]         VARCHAR (255)   NULL,
    [Utm_term]           VARCHAR (255)   NULL,
    [Utm_campaign]       VARCHAR (255)   NULL,
    [NewsletterOffers]   INT             NULL,
    [ProductID]          TINYINT         NOT NULL,
    [CustomerID]         BIGINT          NULL,
    [IsNDNC]             BIT             NULL,
    [IsReleased]         BIT             NULL,
    [CreatedON]          DATETIME        CONSTRAINT [DF_leaddetails150_1_CreatedOn] DEFAULT (getdate()) NOT NULL,
    [UpdatedON]          DATETIME        NULL,
    [ParentID]           BIGINT          NULL,
    [IsActive]           BIT             CONSTRAINT [DF_leaddetails150_1_IsActive] DEFAULT ((1)) NULL,
    [lastVisitedOn]      DATETIME        NULL,
    [LeadRank]           SMALLINT        NULL,
    [ReferralID]         BIGINT          NULL,
    [AddOnParentID]      BIGINT          NULL,
    [PolicyType]         SMALLINT        NULL,
    [ProductType]        SMALLINT        NULL,
    [HasAddOn]           BIT             NULL,
    [IsCTC]              BIT             NULL,
    [LeadScore]          DECIMAL (9, 1)  NULL,
    [LeadGrade]          SMALLINT        NULL,
    [DateOfBirth]        DATE            NULL,
    [FutureProsDate]     DATE            NULL,
    [MKTRevenue]         DECIMAL (18, 3) NULL,
    [LeadType]           VARCHAR (20)    NULL,
    [IsSMS]              BIT             NULL,
    [Model_Score]        FLOAT (53)      NULL,
    [Model_premium]      DECIMAL (18, 3) NULL,
    [ChatStatus]         TINYINT         NULL,
    [IsAllocable]        BIT             NOT NULL,
    [assignedTO]         BIGINT          NULL,
    [IsNameAbused]       BIT             NULL,
    [IsPrimary]          BIT             NOT NULL,
    [AssignedDate]       DATETIME        NULL,
    [StatusID]           TINYINT         NULL,
    [SubStatusID]        SMALLINT        NULL,
    [StatusDate]         DATETIME        NULL,
    [CBDate]             DATETIME        NULL,
    [CBDuration]         TINYINT         NULL,
    [CBType]             TINYINT         NULL,
    [PaymentCB]          BIT             NULL,
    [IsMarkExchange]     TINYINT         CONSTRAINT [DF_leaddetails150_1_IsMarkExchange] DEFAULT ((0)) NULL,
    [Source]             VARCHAR (50)    NULL,
    [LeadCreationSource] VARCHAR (100)   NULL,
    [EnquiryId]          BIGINT          NULL,
    [utm_content]        VARCHAR (255)   NULL,
    CONSTRAINT [PK_leaddetails150_1] PRIMARY KEY CLUSTERED ([LeadID] ASC) WITH (FILLFACTOR = 90)
);


GO
CREATE NONCLUSTERED INDEX [INX_updatedon]
    ON [CRM].[leaddetails150]([UpdatedON] ASC) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [NIX_Mobile_productid]
    ON [CRM].[leaddetails150]([MobileNo] ASC, [ProductID] ASC) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_ProductID_ReferralID]
    ON [CRM].[leaddetails150]([ProductID] ASC, [ReferralID] ASC)
    INCLUDE([ExitPointURL], [Utm_source]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_ProductID_CreatedON_F4453]
    ON [CRM].[leaddetails150]([ProductID] ASC, [CreatedON] ASC)
    INCLUDE([LeadSource], [Utm_source], [ParentID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_ProductID_AssignedDate_DOB_Country_LeadSource]
    ON [CRM].[leaddetails150]([ProductID] ASC, [AssignedDate] ASC, [DOB] ASC, [Country] ASC, [LeadSource] ASC, [CreatedON] ASC, [IsActive] ASC, [StatusID] ASC)
    INCLUDE([LeadID], [MobileNo], [CityID], [AnnualIncome], [Utm_source], [UTM_Medium], [Utm_term], [Utm_campaign], [CustomerID], [LeadRank], [LeadScore], [LeadGrade], [DateOfBirth], [MKTRevenue], [Model_Score], [ChatStatus], [SubStatusID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_ParentID_LeadSource_CreatedON_IsActive_4EFE5]
    ON [CRM].[leaddetails150]([ParentID] ASC, [LeadSource] ASC, [CreatedON] ASC, [IsActive] ASC)
    INCLUDE([LeadID], [DOB], [MobileNo], [CityID], [Country], [AnnualIncome], [Utm_source], [UTM_Medium], [Utm_term], [Utm_campaign], [ProductID], [CustomerID], [DateOfBirth], [Source]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_ParentID_IsActive_LeadRank_AssignedDate_CreatedON_StatusID_LeadID]
    ON [CRM].[leaddetails150]([ParentID] ASC, [IsActive] ASC, [LeadRank] ASC, [AssignedDate] ASC, [CreatedON] ASC, [StatusID] ASC)
    INCLUDE([LeadID], [LeadType]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_LeadSource_ReferralID]
    ON [CRM].[leaddetails150]([LeadSource] ASC, [ReferralID] ASC) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_LeadID_Utm_Term_Utm_source_UTM_Medium_Utm_campaign_CreatedOn]
    ON [CRM].[leaddetails150]([LeadID] ASC, [Utm_term] ASC, [Utm_source] ASC, [UTM_Medium] ASC, [Utm_campaign] ASC, [CreatedON] ASC) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_LeadID_ProductID_CreatedON]
    ON [CRM].[leaddetails150]([LeadID] ASC, [ProductID] ASC, [CreatedON] ASC)
    INCLUDE([Name], [CustomerID], [ParentID], [lastVisitedOn]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_leaddetails150_Utm_campaign_ParentID]
    ON [CRM].[leaddetails150]([Utm_campaign] ASC, [ParentID] ASC)
    INCLUDE([LeadID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_leaddetails150_ProductID_StatusID]
    ON [CRM].[leaddetails150]([ProductID] ASC, [StatusID] ASC)
    INCLUDE([LeadID], [DOB], [LeadSource], [Utm_source], [Utm_campaign], [CustomerID], [CreatedON], [lastVisitedOn], [LeadRank], [LeadScore], [LeadGrade], [MKTRevenue], [Model_Score], [SubStatusID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_LeadDetails150_ProductID_LeadID_MobileNo_IsActive_lastVisitedOn]
    ON [CRM].[leaddetails150]([ProductID] ASC, [LeadID] ASC, [MobileNo] ASC, [IsActive] ASC, [lastVisitedOn] ASC)
    INCLUDE([Name], [Gender], [DOB], [AltPhoneNo], [EmailID], [Address], [CityID], [StateID], [PostCode], [Country], [MaritalStatus], [AnnualIncome], [ProfessionId], [LeadSource], [TrackingID], [ExitID], [HighestBookingStep], [ExitPointURL], [Utm_source], [CreatedON], [UpdatedON], [ParentID], [LeadRank], [ReferralID], [HasAddOn], [IsCTC], [MKTRevenue], [LeadType], [Model_Score], [Source]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_leaddetails150_ParentID_LeadSource_ProductID_CreatedON_IsActive]
    ON [CRM].[leaddetails150]([ParentID] ASC, [LeadSource] ASC, [ProductID] ASC, [CreatedON] ASC, [IsActive] ASC)
    INCLUDE([LeadID], [DOB], [MobileNo], [CityID], [StateID], [Country], [AnnualIncome], [Utm_source], [UTM_Medium], [Utm_term], [Utm_campaign], [CustomerID], [DateOfBirth], [Source], [utm_content]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_leaddetails150_MobileNo_ProductID_CreatedON]
    ON [CRM].[leaddetails150]([MobileNo] ASC, [ProductID] ASC, [CreatedON] ASC)
    INCLUDE([LeadID], [ParentID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_Leaddetails_ProductID_ParentID_IsActive]
    ON [CRM].[leaddetails150]([ProductID] ASC, [ParentID] ASC, [IsActive] ASC)
    INCLUDE([LeadID], [Utm_campaign], [CreatedON], [UpdatedON], [MobileNo], [CustomerID], [StatusID], [SubStatusID], [LeadRank]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_LeadDetails_AddonPrntID]
    ON [CRM].[leaddetails150]([AddOnParentID] ASC)
    INCLUDE([LeadID], [ProductID], [IsActive]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_CreatedON_lastVisitedOn]
    ON [CRM].[leaddetails150]([CreatedON] ASC, [lastVisitedOn] ASC)
    INCLUDE([LeadID], [Name], [MobileNo], [ProductID], [CustomerID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_assignedTO_AssignedDate_StatusID]
    ON [CRM].[leaddetails150]([assignedTO] ASC, [AssignedDate] ASC, [StatusID] ASC)
    INCLUDE([LeadID], [CustomerID], [Name]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [IX_AppointMnet_Allocation]
    ON [CRM].[leaddetails150]([ParentID] ASC, [MobileNo] ASC, [LeadSource] ASC, [CreatedON] ASC, [IsActive] ASC)
    INCLUDE([LeadID], [ProductID], [LeadRank]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [INX_SRCH_ProductID]
    ON [CRM].[leaddetails150]([ProductID] ASC, [CustomerID] ASC, [CreatedON] ASC, [ParentID] ASC, [StatusID] ASC)
    INCLUDE([LeadID], [MobileNo], [IsActive], [Name], [LeadSource]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [inx_productid_assgnto_customerid_createdon_]
    ON [CRM].[leaddetails150]([ProductID] ASC, [assignedTO] ASC, [CustomerID] ASC, [CreatedON] ASC)
    INCLUDE([LeadID], [Name], [MobileNo], [EmailID], [CityID], [AnnualIncome], [LeadSource], [ParentID], [IsActive], [lastVisitedOn], [ReferralID], [ProductType], [IsCTC], [LeadType], [AssignedDate], [StatusID], [UTM_Medium]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [inx_prentid_leadsource_createdon_isactive_lid_dob_mno_cid_ai_us_um_pid_cid]
    ON [CRM].[leaddetails150]([ParentID] ASC, [LeadSource] ASC, [CreatedON] ASC, [IsActive] ASC)
    INCLUDE([LeadID], [DOB], [MobileNo], [CityID], [AnnualIncome], [Utm_source], [UTM_Medium], [ProductID], [CustomerID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [INX_ParenTID]
    ON [CRM].[leaddetails150]([ParentID] ASC)
    INCLUDE([LeadID], [Name], [Gender], [DOB], [MobileNo], [AltPhoneNo], [EmailID], [Address], [CityID], [PostCode], [MaritalStatus], [AnnualIncome], [ProfessionId], [LeadSource], [TrackingID], [ExitID], [HighestBookingStep], [ExitPointURL], [Utm_source], [ProductID], [CreatedON], [UpdatedON], [Country]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [INx_moble_Actve]
    ON [CRM].[leaddetails150]([MobileNo] ASC, [IsActive] ASC, [CreatedON] ASC)
    INCLUDE([lastVisitedOn]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [INX_LEADID_CID_N_G_M_A_E_A_PC]
    ON [CRM].[leaddetails150]([LeadID] ASC, [CityID] ASC)
    INCLUDE([Name], [Gender], [MobileNo], [AltPhoneNo], [EmailID], [Address], [PostCode]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [inx_leadid]
    ON [CRM].[leaddetails150]([LeadID] ASC, [CreatedON] ASC)
    INCLUDE([Name], [MobileNo], [EmailID], [ProductID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [Inx_Enquiryid]
    ON [CRM].[leaddetails150]([EnquiryId] ASC) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [INX_CustID_MblNO]
    ON [CRM].[leaddetails150]([CustomerID] ASC, [ProductID] ASC, [CreatedON] ASC, [StatusID] ASC)
    INCLUDE([LeadID], [Name], [ParentID]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [INX_Cretedon_ISActve]
    ON [CRM].[leaddetails150]([CreatedON] ASC, [LeadID] ASC, [IsActive] ASC)
    INCLUDE([Name], [ProductID], [CustomerID], [lastVisitedOn]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE NONCLUSTERED INDEX [_IX_LeadDetatils_Agentmenus]
    ON [CRM].[leaddetails150]([LeadID] ASC, [ProductID] ASC, [MobileNo] ASC, [LeadSource] ASC)
    INCLUDE([EmailID], [Name]) WITH (FILLFACTOR = 90)
    ON [SecIndex];


GO
CREATE STATISTICS [_WA_Sys_00000018_7BE19B47]
    ON [CRM].[leaddetails150]([Utm_term]);


GO
CREATE STATISTICS [_WA_Sys_00000016_7BE19B47]
    ON [CRM].[leaddetails150]([Utm_source]);


GO
CREATE STATISTICS [_WA_Sys_00000017_7BE19B47]
    ON [CRM].[leaddetails150]([UTM_Medium]);


GO
CREATE STATISTICS [_WA_Sys_00000045_7BE19B47]
    ON [CRM].[leaddetails150]([utm_content]);


GO
CREATE STATISTICS [_WA_Sys_00000019_7BE19B47]
    ON [CRM].[leaddetails150]([Utm_campaign]);


GO
CREATE STATISTICS [_WA_Sys_00000020_7BE19B47]
    ON [CRM].[leaddetails150]([UpdatedON]);


GO
CREATE STATISTICS [_WA_Sys_0000003B_7BE19B47]
    ON [CRM].[leaddetails150]([SubStatusID]);


GO
CREATE STATISTICS [_WA_Sys_0000003A_7BE19B47]
    ON [CRM].[leaddetails150]([StatusID]);


GO
CREATE STATISTICS [_WA_Sys_0000003C_7BE19B47]
    ON [CRM].[leaddetails150]([StatusDate]);


GO
CREATE STATISTICS [_WA_Sys_0000000B_7BE19B47]
    ON [CRM].[leaddetails150]([StateID]);


GO
CREATE STATISTICS [_WA_Sys_00000042_7BE19B47]
    ON [CRM].[leaddetails150]([Source]);


GO
CREATE STATISTICS [_WA_Sys_00000002_7BE19B47]
    ON [CRM].[leaddetails150]([SessionID]);


GO
CREATE STATISTICS [_WA_Sys_00000025_7BE19B47]
    ON [CRM].[leaddetails150]([ReferralID]);


GO
CREATE STATISTICS [_WA_Sys_00000028_7BE19B47]
    ON [CRM].[leaddetails150]([ProductType]);


GO
CREATE STATISTICS [_WA_Sys_0000001B_7BE19B47]
    ON [CRM].[leaddetails150]([ProductID]);


GO
CREATE STATISTICS [_WA_Sys_0000000C_7BE19B47]
    ON [CRM].[leaddetails150]([PostCode]);


GO
CREATE STATISTICS [_WA_Sys_00000040_7BE19B47]
    ON [CRM].[leaddetails150]([PaymentCB]);


GO
CREATE STATISTICS [_WA_Sys_00000021_7BE19B47]
    ON [CRM].[leaddetails150]([ParentID]);


GO
CREATE STATISTICS [_WA_Sys_00000003_7BE19B47]
    ON [CRM].[leaddetails150]([Name]);


GO
CREATE STATISTICS [_WA_Sys_00000032_7BE19B47]
    ON [CRM].[leaddetails150]([Model_Score]);


GO
CREATE STATISTICS [_WA_Sys_00000006_7BE19B47]
    ON [CRM].[leaddetails150]([MobileNo]);


GO
CREATE STATISTICS [_WA_Sys_0000002F_7BE19B47]
    ON [CRM].[leaddetails150]([MKTRevenue]);


GO
CREATE STATISTICS [_WA_Sys_00000011_7BE19B47]
    ON [CRM].[leaddetails150]([LeadSource]);


GO
CREATE STATISTICS [_WA_Sys_0000002B_7BE19B47]
    ON [CRM].[leaddetails150]([LeadScore]);


GO
CREATE STATISTICS [_WA_Sys_00000024_7BE19B47]
    ON [CRM].[leaddetails150]([LeadRank]);


GO
CREATE STATISTICS [_WA_Sys_00000001_7BE19B47]
    ON [CRM].[leaddetails150]([LeadID]);


GO
CREATE STATISTICS [_WA_Sys_0000002C_7BE19B47]
    ON [CRM].[leaddetails150]([LeadGrade]);


GO
CREATE STATISTICS [_WA_Sys_00000043_7BE19B47]
    ON [CRM].[leaddetails150]([LeadCreationSource]);


GO
CREATE STATISTICS [_WA_Sys_00000023_7BE19B47]
    ON [CRM].[leaddetails150]([lastVisitedOn]);


GO
CREATE STATISTICS [_WA_Sys_00000041_7BE19B47]
    ON [CRM].[leaddetails150]([IsMarkExchange]);


GO
CREATE STATISTICS [_WA_Sys_0000002A_7BE19B47]
    ON [CRM].[leaddetails150]([IsCTC]);


GO
CREATE STATISTICS [_WA_Sys_00000022_7BE19B47]
    ON [CRM].[leaddetails150]([IsActive]);


GO
CREATE STATISTICS [_WA_Sys_0000002E_7BE19B47]
    ON [CRM].[leaddetails150]([FutureProsDate]);


GO
CREATE STATISTICS [_WA_Sys_00000015_7BE19B47]
    ON [CRM].[leaddetails150]([ExitPointURL]);


GO
CREATE STATISTICS [_WA_Sys_00000044_7BE19B47]
    ON [CRM].[leaddetails150]([EnquiryId]);


GO
CREATE STATISTICS [_WA_Sys_00000008_7BE19B47]
    ON [CRM].[leaddetails150]([EmailID]);


GO
CREATE STATISTICS [_WA_Sys_00000005_7BE19B47]
    ON [CRM].[leaddetails150]([DOB]);


GO
CREATE STATISTICS [_WA_Sys_0000002D_7BE19B47]
    ON [CRM].[leaddetails150]([DateOfBirth]);


GO
CREATE STATISTICS [_WA_Sys_0000001C_7BE19B47]
    ON [CRM].[leaddetails150]([CustomerID]);


GO
CREATE STATISTICS [_WA_Sys_0000001F_7BE19B47]
    ON [CRM].[leaddetails150]([CreatedON]);


GO
CREATE STATISTICS [_WA_Sys_0000000D_7BE19B47]
    ON [CRM].[leaddetails150]([Country]);


GO
CREATE STATISTICS [_WA_Sys_0000000A_7BE19B47]
    ON [CRM].[leaddetails150]([CityID]);


GO
CREATE STATISTICS [_WA_Sys_00000034_7BE19B47]
    ON [CRM].[leaddetails150]([ChatStatus]);


GO
CREATE STATISTICS [_WA_Sys_0000003F_7BE19B47]
    ON [CRM].[leaddetails150]([CBType]);


GO
CREATE STATISTICS [_WA_Sys_0000003E_7BE19B47]
    ON [CRM].[leaddetails150]([CBDuration]);


GO
CREATE STATISTICS [_WA_Sys_0000003D_7BE19B47]
    ON [CRM].[leaddetails150]([CBDate]);


GO
CREATE STATISTICS [_WA_Sys_00000036_7BE19B47]
    ON [CRM].[leaddetails150]([assignedTO]);


GO
CREATE STATISTICS [_WA_Sys_00000039_7BE19B47]
    ON [CRM].[leaddetails150]([AssignedDate]);


GO
CREATE STATISTICS [_WA_Sys_0000000F_7BE19B47]
    ON [CRM].[leaddetails150]([AnnualIncome]);


GO
CREATE STATISTICS [_WA_Sys_00000007_7BE19B47]
    ON [CRM].[leaddetails150]([AltPhoneNo]);


GO
CREATE STATISTICS [_WA_Sys_00000026_7BE19B47]
    ON [CRM].[leaddetails150]([AddOnParentID]);


GO
DENY SELECT
    ON [CRM].[leaddetails150] ([EmailID]) TO [denypermission]
    AS [dbo];


GO
DENY SELECT
    ON [CRM].[leaddetails150] ([AltPhoneNo]) TO [denypermission]
    AS [dbo];


GO
DENY SELECT
    ON [CRM].[leaddetails150] ([MobileNo]) TO [denypermission]
    AS [dbo];


GO
GRANT DELETE
    ON OBJECT::[CRM].[leaddetails150] TO [backofficesys]
    AS [dbo];

