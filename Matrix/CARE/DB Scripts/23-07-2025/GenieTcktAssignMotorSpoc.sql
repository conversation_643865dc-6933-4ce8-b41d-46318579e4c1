USE [Matrix]
GO

-- JOURNEY
UPDATE Matrix.CARE.SalesUserProcessMapping set IsActive = 0 where UserID IN (37,31,5607,8437,6523) AND ProcessID = 2 AND ProductID = 117;

-- L1

DECLARE @ticketUserId BIGINT = 0;
INSERT INTO Matrix.CARE.SalesUserDetails (MatrixUserID,UserLevel,TicketRoleID,CreatedOn,IsActive) VALUES(69403,1,1,GETDATE(),1)
SET @ticketUserId = SCOPE_IDENTITY()
INSERT INTO Matrix.CARE.SalesUserProcessMapping(UserID,ProcessID,ProductID,IsActive,CreatedOn,subprocessId) VALUES(@ticketUserId,2,117,1,getdate(),0);

-- L2
UPDATE Matrix.CARE.SalesUserDetails set UserLevel = 2, TicketRoleID = 1, IsActive = 1 where TicketUserID = 8272;
INSERT INTO Matrix.CARE.SalesUserProcessMapping(UserID,ProcessID,ProductID,IsActive,CreatedOn,subprocessId) VALUES(8272,2,117,1,getdate(),0);


-- TRAINING
--L1
UPDATE Matrix.CARE.SalesUserDetails set UserLevel = 1, TicketRoleID = 1, IsActive = 1 where TicketUserID = 6104;
INSERT INTO Matrix.CARE.SalesUserProcessMapping(UserID,ProcessID,ProductID,IsActive,CreatedOn,subprocessId) VALUES(6104,6,117,1,getdate(),0);

-- MY REWARDS
--Jag (L2)
INSERT INTO Matrix.CARE.SalesUserProcessMapping(UserID,ProcessID,ProductID,IsActive,CreatedOn,subprocessId) VALUES(16392,7,117,1,getdate(),36);

--Contest(L1)
UPDATE Matrix.CARE.SalesUserProcessMapping set IsActive = 0 where UserID = 13998 AND ProcessID = 7 AND ProductID = 117 AND SubProcessID = 37;

UPDATE Matrix.CARE.SalesUserDetails set UserLevel = 1, TicketRoleID = 1, IsActive = 1 where TicketUserID = 20025;
INSERT INTO Matrix.CARE.SalesUserProcessMapping(UserID,ProcessID,ProductID,IsActive,CreatedOn,subprocessId) VALUES(20025,7,117,1,getdate(),37);

--Contest(L2)
UPDATE Matrix.CARE.SalesUserDetails set UserLevel = 2, TicketRoleID = 1, IsActive = 1 where TicketUserID = 7818;
INSERT INTO Matrix.CARE.SalesUserProcessMapping(UserID,ProcessID,ProductID,IsActive,CreatedOn,subprocessId) VALUES(7818,7,117,1,getdate(),37);

--Incentive(L1,L2)
UPDATE Matrix.CARE.SalesUserProcessMapping set IsActive = 0 where UserID IN (13961,9542) AND ProcessID = 7 AND ProductID = 117 AND SubProcessID = 35;

INSERT INTO Matrix.CARE.SalesUserProcessMapping(UserID,ProcessID,ProductID,IsActive,CreatedOn,subprocessId) VALUES(20025,7,117,1,getdate(),35);
INSERT INTO Matrix.CARE.SalesUserProcessMapping(UserID,ProcessID,ProductID,IsActive,CreatedOn,subprocessId) VALUES(7818,7,117,1,getdate(),35);

--FOS
-- L2
INSERT INTO Matrix.CARE.SalesUserProcessMapping(UserID,ProcessID,ProductID,IsActive,CreatedOn,subprocessId) VALUES(8272,8,117,1,getdate(),0);

-- SERVICE ESCALATION
-- L1
INSERT INTO Matrix.CARE.SalesUserDetails (MatrixUserID,UserLevel,TicketRoleID,CreatedOn,IsActive) VALUES(6684,1,1,GETDATE(),1);
SET @ticketUserId = SCOPE_IDENTITY()
INSERT INTO Matrix.CARE.SalesUserProcessMapping(UserID,ProcessID,ProductID,IsActive,CreatedOn,subprocessId) VALUES(@ticketUserId,11,117,1,getdate(),0);

--L2
UPDATE Matrix.CARE.SalesUserDetails set IsActive = 1 where TicketUserID = 3264;
INSERT INTO Matrix.CARE.SalesUserProcessMapping(UserID,ProcessID,ProductID,IsActive,CreatedOn,subprocessId) VALUES(3264,11,117,1,getdate(),0);


-- PRICING
INSERT INTO MATRIX.CARE.SalesTicketMaster VALUES ('Process',14,'Pricing');
INSERT INTO [Matrix].[CARE].[SalesIssueMaster] VALUES ('Change in Premium on Proposal Page',1,14,14400);
INSERT INTO Matrix.CARE.SalesIssueMaster VALUES ('Better Price outside',1,14,14400);
INSERT INTO Matrix.CARE.SalesIssueMaster VALUES ('Others',1,14,14400);

-- L1
INSERT INTO Matrix.CARE.SalesUserDetails (MatrixUserID,UserLevel,TicketRoleID,CreatedOn,IsActive) VALUES(58534,1,1,GETDATE(),1);
SET @ticketUserId = SCOPE_IDENTITY()
INSERT INTO Matrix.CARE.SalesUserProcessMapping(UserID,ProcessID,ProductID,IsActive,CreatedOn,subprocessId) VALUES(@ticketUserId,14,117,1,getdate(),0);

--L2
INSERT INTO Matrix.CARE.SalesUserProcessMapping(UserID,ProcessID,ProductID,IsActive,CreatedOn,subprocessId) VALUES(8437,14,117,1,getdate(),0);

GO
